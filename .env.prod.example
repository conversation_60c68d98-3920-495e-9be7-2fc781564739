# Production Environment Variables Example
# Copy this to .env on your production server and update the values

# Application
NODE_ENV=production
BACKEND_PORT=5000
ENABLE_AUTH=TRUE
JWT_SECRET=your-super-secure-jwt-secret-here
JWT_EXPIRES=7d

# External Database Configuration
DB_HOST=your-external-db-host
DB_PORT=5432
DB_NAME=fes_crm_prod
DB_USER=your-db-username
DB_PASSWORD=your-db-password

# Full database URL (update with your external database details)
# Note: Replace the placeholders below with actual values - do not use variable substitution
DATABASE_URL="***********************************************************************/fes_crm_prod"

# Performance Tuning
NODE_OPTIONS="--max-old-space-size=2048"
UV_THREADPOOL_SIZE=128

# Monitoring and Alerting
SLACK_WEBHOOK=
ALERT_EMAIL=<EMAIL>
SENDGRID_API_KEY=SG.dZpRYVOTSFq1C7oF15WGMw.ererwer

# Production Configuration
PROD_DOMAIN=https://api.crm.fespakportal.com

# Deployment Configuration
DEPLOYMENT_VERSION=latest
DEPLOYMENT_TIMESTAMP=$(date -Iseconds)

# Security Configuration
TRUSTED_PROXIES=127.0.0.1,::1
CORS_ORIGINS=https://crm.fespakportal.com

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=100m
LOG_MAX_FILES=5
