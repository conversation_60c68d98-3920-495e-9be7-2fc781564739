name: Zero-Downtime Production Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean
      rollback_version:
        description: 'Rollback to specific version (commit SHA)'
        required: false
        type: string

permissions:
  contents: read
  security-events: write
  actions: read

env:
  DEPLOYMENT_TIMEOUT: 600
  HEALTH_CHECK_RETRIES: 30
  HEALTH_CHECK_INTERVAL: 10
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

jobs:
  security-scan:
    name: Security & Vulnerability Scan
    runs-on: ubuntu-latest
    outputs:
      security-passed: ${{ steps.security-check.outputs.passed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
        continue-on-error: true
        id: trivy-scan
        env:
          TRIVY_DISABLE_TELEMETRY: true

      - name: Upload Trivy scan results to GitHub Security
        uses: github/codeql-action/upload-sarif@v3
        if: always() && github.event_name != 'pull_request'
        continue-on-error: true
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Display scan results
        if: always()
        run: |
          echo "Trivy scan completed with outcome: ${{ steps.trivy-scan.outcome }}"
          if [ -f "trivy-results.sarif" ]; then
            echo "SARIF file generated successfully"
            # Display summary of findings
            if command -v jq &> /dev/null; then
              CRITICAL_COUNT=$(jq '.runs[0].results | map(select(.level == "error")) | length' trivy-results.sarif 2>/dev/null || echo "0")
              HIGH_COUNT=$(jq '.runs[0].results | map(select(.level == "warning")) | length' trivy-results.sarif 2>/dev/null || echo "0")
              echo "Critical vulnerabilities found: $CRITICAL_COUNT"
              echo "High vulnerabilities found: $HIGH_COUNT"
            fi
          else
            echo "No SARIF file generated"
          fi

      - name: Security check result
        id: security-check
        run: |
          # Always allow deployment to proceed, but report security status
          echo "passed=true" >> $GITHUB_OUTPUT
          if [ "${{ steps.trivy-scan.outcome }}" == "success" ]; then
            echo "✅ Security scan passed - no critical/high vulnerabilities found"
          else
            echo "⚠️ Security scan found vulnerabilities but deployment will continue"
            echo "Please review security findings in the GitHub Security tab"
            echo "Consider addressing vulnerabilities in a future release"
          fi

      - name: Notify security findings
        if: always() && steps.trivy-scan.outcome != 'success' && env.SLACK_WEBHOOK != ''
        run: |
          # Get vulnerability counts if available
          CRITICAL_COUNT="0"
          HIGH_COUNT="0"
          if [ -f "trivy-results.sarif" ] && command -v jq &> /dev/null; then
            CRITICAL_COUNT=$(jq '.runs[0].results | map(select(.level == "error")) | length' trivy-results.sarif 2>/dev/null || echo "0")
            HIGH_COUNT=$(jq '.runs[0].results | map(select(.level == "warning")) | length' trivy-results.sarif 2>/dev/null || echo "0")
          fi

          curl -X POST -H 'Content-type: application/json' \
            --data "{
              \"attachments\": [{
                \"color\": \"warning\",
                \"title\": \"⚠️ Security Vulnerabilities Detected\",
                \"fields\": [
                  {\"title\": \"Critical\", \"value\": \"$CRITICAL_COUNT\", \"short\": true},
                  {\"title\": \"High\", \"value\": \"$HIGH_COUNT\", \"short\": true},
                  {\"title\": \"Commit\", \"value\": \"${{ github.sha }}\", \"short\": true},
                  {\"title\": \"Branch\", \"value\": \"${{ github.ref_name }}\", \"short\": true}
                ],
                \"text\": \"Security scan found vulnerabilities. Deployment will continue, but please review findings in GitHub Security tab.\",
                \"footer\": \"GitHub Actions Security Scan\",
                \"ts\": $(date +%s)
              }]
            }" \
            ${{ env.SLACK_WEBHOOK }}

  build-and-test:
    name: Build & Test Application
    runs-on: ubuntu-latest
    needs: security-scan
    if: needs.security-scan.outputs.security-passed == 'true'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test -- --coverage

      - name: Build application
        run: npm run build

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: fes-crm-backend
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build Docker image
        id: build
        run: |
          docker build -f Dockerfile.prod -t fes-crm-backend:${{ github.sha }} .
          docker tag fes-crm-backend:${{ github.sha }} fes-crm-backend:latest
          echo "digest=$(docker images --digests fes-crm-backend:${{ github.sha }} --format '{{.Digest}}')" >> $GITHUB_OUTPUT

      - name: Save Docker image
        run: |
          docker save fes-crm-backend:${{ github.sha }} | gzip > fes-crm-backend-${{ github.sha }}.tar.gz

      - name: Upload Docker image artifact
        uses: actions/upload-artifact@v4
        with:
          name: docker-image-${{ github.sha }}
          path: fes-crm-backend-${{ github.sha }}.tar.gz
          retention-days: 7

  deploy:
    name: Zero-Downtime Deployment
    runs-on: ubuntu-latest
    needs: [ security-scan, build-and-test ]
    if: needs.security-scan.outputs.security-passed == 'true'
    environment:
      name: production
      url: ${{ vars.PROD_DOMAIN }}
    outputs:
      deployment-status: ${{ steps.deployment-result.outputs.status }}
      previous-version: ${{ steps.set-version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Docker image
        uses: actions/download-artifact@v4
        with:
          name: docker-image-${{ github.sha }}
          path: .

      - name: Verify downloaded files
        run: |
          echo "=== Workspace contents ==="
          ls -la
          echo "=== Checking for Docker image ==="
          if [ -f "fes-crm-backend-${{ github.sha }}.tar.gz" ]; then
            echo "✅ Docker image found: fes-crm-backend-${{ github.sha }}.tar.gz"
            ls -lh "fes-crm-backend-${{ github.sha }}.tar.gz"
          else
            echo "❌ Docker image NOT found: fes-crm-backend-${{ github.sha }}.tar.gz"
            exit 1
          fi
          echo "=== Checking other required files ==="
          for file in docker-compose.prod.yml Dockerfile.prod entrypoint.prod.sh package.json package-lock.json; do
            if [ -f "$file" ]; then
              echo "✅ Found: $file"
            else
              echo "❌ Missing: $file"
            fi
          done
          echo "=== Checking directories ==="
          for dir in prisma src scripts; do
            if [ -d "$dir" ]; then
              echo "✅ Found directory: $dir"
            else
              echo "❌ Missing directory: $dir"
            fi
          done

      - name: Notify deployment start
        if: env.SLACK_WEBHOOK != ''
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Starting zero-downtime deployment of FES CRM Backend\nCommit: ${{ github.sha }}\nBranch: ${{ github.ref_name }}\nActor: ${{ github.actor }}"}' \
            ${{ env.SLACK_WEBHOOK }}

      - name: Create deployment package
        run: |
          echo "=== Creating deployment package ==="
          # Create a clean deployment directory
          mkdir -p deployment-package

          # Copy all necessary files to deployment package
          cp docker-compose.prod.yml deployment-package/
          cp Dockerfile.prod deployment-package/
          cp entrypoint.prod.sh deployment-package/
          cp package*.json deployment-package/
          cp -r prisma deployment-package/
          cp -r src deployment-package/
          cp -r scripts deployment-package/
          cp fes-crm-backend-${{ github.sha }}.tar.gz deployment-package/

          echo "=== Deployment package contents ==="
          ls -la deployment-package/

          # Create a tar.gz of the deployment package
          tar -czf deployment-package.tar.gz -C deployment-package .
          ls -lh deployment-package.tar.gz

      - name: Copy deployment package to production server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          source: "deployment-package.tar.gz"
          target: /root/
          overwrite: true

      - name: Extract deployment package on server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            echo "=== Extracting deployment package ==="
            cd /root

            # Remove old deployment directory if exists
            rm -rf fes-crm-backend-new

            # Create new deployment directory
            mkdir -p fes-crm-backend-new

            # Extract deployment package
            tar -xzf deployment-package.tar.gz -C fes-crm-backend-new

            # Verify extraction
            echo "=== Deployment directory contents ==="
            ls -la fes-crm-backend-new/

            # Cleanup
            rm -f deployment-package.tar.gz

      - name: Final verification of deployment files
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            echo "=== Final verification of deployment files ==="
            cd /root/fes-crm-backend-new

            echo "=== Checking required files ==="
            for file in docker-compose.prod.yml Dockerfile.prod entrypoint.prod.sh package.json package-lock.json; do
              if [ -f "$file" ]; then
                echo "✅ Found: $file"
              else
                echo "❌ Missing: $file"
                exit 1
              fi
            done

            echo "=== Checking required directories ==="
            for dir in prisma src scripts; do
              if [ -d "$dir" ]; then
                echo "✅ Found directory: $dir"
              else
                echo "❌ Missing directory: $dir"
                exit 1
              fi
            done

            echo "=== Checking Docker image ==="
            if [ -f "fes-crm-backend-${{ github.sha }}.tar.gz" ]; then
              echo "✅ Docker image found on server"
              ls -lh "fes-crm-backend-${{ github.sha }}.tar.gz"
            else
              echo "❌ Docker image NOT found on server"
              exit 1
            fi

            echo "✅ All deployment files verified successfully"

      - name: Get current version
        id: get-current-version
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            if [ -f /root/fes-crm-backend/.version ]; then
              cat /root/fes-crm-backend/.version
            else
              echo "none"
            fi

      - name: Set current version output
        id: set-version
        run: |
          VERSION="${{ steps.get-current-version.outputs.stdout }}"
          # Remove any trailing whitespace/newlines
          VERSION=$(echo "$VERSION" | tr -d '\n\r' | xargs)
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Zero-downtime deployment
        id: deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          timeout: ${{ env.DEPLOYMENT_TIMEOUT }}s
          script: |
            set -e

            # Colors for output
            RED='\033[0;31m'
            GREEN='\033[0;32m'
            YELLOW='\033[1;33m'
            NC='\033[0m'

            log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
            warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
            error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }

            # Configuration
            NEW_VERSION="${{ github.sha }}"
            APP_DIR="/root/fes-crm-backend"
            NEW_DIR="/root/fes-crm-backend-new"
            BACKUP_DIR="/root/fes-crm-backend-backup-$(date +%Y%m%d-%H%M%S)"

            log "Starting zero-downtime deployment for version ${NEW_VERSION}"

            # Create deployment directory structure
            mkdir -p "${NEW_DIR}"
            cd "${NEW_DIR}"

            # Load Docker image
            log "Loading new Docker image..."
            sudo docker load < fes-crm-backend-${NEW_VERSION}.tar.gz

            # Backup and preserve environment file
            if [ -f "${APP_DIR}/.env" ]; then
              # Create backup of current .env
              cp "${APP_DIR}/.env" "${NEW_DIR}/.env.backup"
              cp "${APP_DIR}/.env" "${NEW_DIR}/.env"
              log "Environment file backed up and copied from current deployment"
            else
              error "No .env file found in current deployment"
              error "Please ensure .env file exists on the server before deployment"
              exit 1
            fi

            # Set permissions
            chmod +x entrypoint.prod.sh
            sudo chown -R $USER:$USER "${NEW_DIR}"

            # Clean up conflicting networks first
            log "Cleaning up conflicting Docker networks..."
            sudo docker network rm clones-backend_clone_backend 2>/dev/null || true
            log "Network cleanup completed"

            # Database migration safety check
            log "Checking database migration safety..."
            # Safely load environment variables from .env file
            set -a  # automatically export all variables
            source .env
            set +a  # stop automatically exporting

            # Create backup of current deployment
            if [ -d "${APP_DIR}" ]; then
              log "Creating backup of current deployment..."
              sudo cp -r "${APP_DIR}" "${BACKUP_DIR}"
            fi

            # Run database migrations in a safe way
            log "Running database migrations..."
            sudo docker run --rm \
              --env-file .env \
              --network host \
              fes-crm-backend:${NEW_VERSION} \
              sh -c "npx prisma migrate deploy --schema=./prisma/schema.prisma"

            if [ $? -ne 0 ]; then
              error "Database migration failed"
              exit 1
            fi

            # Zero-downtime deployment using improved strategy
            log "Starting zero-downtime deployment..."

            # Step 1: Create green environment using standalone container
            export GREEN_PORT=5001
            export BLUE_PORT=5000

            # Get or create the production network
            NETWORK_NAME="fes-crm-backend_fes_crm_network"
            if ! sudo docker network ls --format "{{.Name}}" | grep -q "^${NETWORK_NAME}$"; then
              log "Creating production network..."
              sudo docker network create \
                --driver bridge \
                --subnet **********/16 \
                --label "com.docker.compose.network=fes_crm" \
                "$NETWORK_NAME"
            fi

            # Stop any existing green container
            sudo docker stop fes_crm_green 2>/dev/null || true
            sudo docker rm fes_crm_green 2>/dev/null || true

            # Start green container for testing
            log "Starting green environment on port ${GREEN_PORT}..."
            sudo docker run -d \
              --name fes_crm_green \
              --network "$NETWORK_NAME" \
              -p "${GREEN_PORT}:5000" \
              --env-file .env \
              --restart unless-stopped \
              --health-cmd "curl -f http://localhost:5000/ || exit 1" \
              --health-interval 15s \
              --health-timeout 5s \
              --health-retries 3 \
              --health-start-period 30s \
              fes-crm-backend:${NEW_VERSION}

            # Wait for containers to be ready
            log "Waiting for green environment to be ready..."
            sleep 30

            # Health check function
            health_check() {
              local port=$1
              local retries=$2
              local interval=$3

              for i in $(seq 1 $retries); do
                if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
                  log "Health check passed on port ${port} (attempt ${i}/${retries})"
                  return 0
                fi
                warn "Health check failed on port ${port} (attempt ${i}/${retries})"
                sleep $interval
              done

              error "Health check failed after ${retries} attempts on port ${port}"
              return 1
            }

            # Comprehensive health checks for green environment
            log "Running comprehensive health checks on green environment..."

            if ! health_check ${GREEN_PORT} ${{ env.HEALTH_CHECK_RETRIES }} ${{ env.HEALTH_CHECK_INTERVAL }}; then
              error "Green environment health check failed"

              # Cleanup failed green deployment
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              sudo docker image rm fes-crm-backend:${NEW_VERSION} || true

              exit 1
            fi

            # Run smoke tests
            log "Running smoke tests on green environment..."

            # Test API endpoints
            if ! curl -f -s "http://localhost:${GREEN_PORT}/" | grep -q "Hello World"; then
              error "Smoke test failed: API not responding correctly"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              exit 1
            fi

            # Test database connectivity
            if ! sudo docker compose -f docker-compose.prod.yml exec -T backend npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then
              error "Smoke test failed: Database connectivity issue"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              exit 1
            fi

            log "All smoke tests passed on green environment"

            # Traffic switch: Stop blue environment and start green on main port
            log "Switching traffic from blue to green environment..."

            # Stop current blue environment
            if [ -d "${APP_DIR}" ]; then
              cd "${APP_DIR}"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans || true
            fi

            # Wait for network cleanup
            log "Waiting for network cleanup..."
            sleep 5

            # Move new deployment to main directory while preserving .env
            cd /root

            # Backup current .env one more time before moving
            if [ -f "${APP_DIR}/.env" ]; then
              cp "${APP_DIR}/.env" "/tmp/.env.production.backup"
              log "Final .env backup created at /tmp/.env.production.backup"
            fi

            if [ -d "${APP_DIR}" ]; then
              rm -rf "${APP_DIR}"
            fi
            mv "${NEW_DIR}" "${APP_DIR}"

            # Ensure .env is in place and has correct permissions
            if [ -f "${APP_DIR}/.env" ]; then
              chown root:root "${APP_DIR}/.env"
              chmod 600 "${APP_DIR}/.env"
              log "Environment file restored with correct permissions"
            else
              error "Environment file missing after deployment move"
              # Restore from backup if available
              if [ -f "/tmp/.env.backup" ]; then
                cp "/tmp/.env.backup" "${APP_DIR}/.env"
                chown root:root "${APP_DIR}/.env"
                chmod 600 "${APP_DIR}/.env"
                log "Environment file restored from backup"
              else
                error "No backup .env file available for restoration"
                exit 1
              fi
            fi

            # Update docker-compose to use main port
            cd "${APP_DIR}"
            sed -i "s|${GREEN_PORT}:5000|5000:5000|g" docker-compose.prod.yml
            sed -i "s|fes_crm_green|fes_crm_prod|g" docker-compose.prod.yml

            # Start on main port
            sudo docker compose -f docker-compose.prod.yml up -d

            # Final health check on main port
            log "Running final health check on main port..."

            if ! health_check 5000 ${{ env.HEALTH_CHECK_RETRIES }} ${{ env.HEALTH_CHECK_INTERVAL }}; then
              error "Final health check failed on main port"

              # Emergency rollback
              if [ -d "${BACKUP_DIR}" ]; then
                warn "Performing emergency rollback..."
                sudo docker compose -f docker-compose.prod.yml down --remove-orphans

                # Preserve current .env before rollback
                if [ -f "${APP_DIR}/.env" ]; then
                  cp "${APP_DIR}/.env" "/tmp/.env.emergency.backup"
                fi

                rm -rf "${APP_DIR}"
                mv "${BACKUP_DIR}" "${APP_DIR}"

                # Restore .env file after rollback
                if [ -f "/tmp/.env.emergency.backup" ]; then
                  cp "/tmp/.env.emergency.backup" "${APP_DIR}/.env"
                  chown root:root "${APP_DIR}/.env"
                  chmod 600 "${APP_DIR}/.env"
                  log "Environment file restored after emergency rollback"
                fi

                cd "${APP_DIR}"
                sudo docker compose -f docker-compose.prod.yml up -d
              fi

              exit 1
            fi

            # Save version info
            echo "${NEW_VERSION}" > "${APP_DIR}/.version"
            echo "$(date)" > "${APP_DIR}/.deployment-time"

            # Cleanup
            log "Cleaning up old images and containers..."
            sudo docker system prune -f
            sudo docker image prune -f

            # Clean up temporary directories
            log "Cleaning up temporary deployment directories..."
            find /root -maxdepth 1 -name "fes-crm-backend-new*" -type d -exec rm -rf {} \; 2>/dev/null || true

            # Remove old backups (keep last 3)
            log "Managing backup directories (keeping latest 3)..."
            BACKUP_COUNT=$(find /root -name "fes-crm-backend-backup-*" -type d | wc -l)
            if [ "$BACKUP_COUNT" -gt 3 ]; then
              REMOVE_COUNT=$((BACKUP_COUNT - 3))
              find /root -name "fes-crm-backend-backup-*" -type d | sort | head -n "$REMOVE_COUNT" | xargs sudo rm -rf
              log "Removed $REMOVE_COUNT old backup directories"
            fi

            log "Zero-downtime deployment completed successfully!"
            log "New version ${NEW_VERSION} is now live"

            # Display final status
            sudo docker compose -f docker-compose.prod.yml ps

      - name: Post-deployment validation
        id: validation
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            log() { echo -e "\033[0;32m[$(date +'%Y-%m-%d %H:%M:%S')] $1\033[0m"; }
            error() { echo -e "\033[0;31m[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1\033[0m"; }

            log "Running post-deployment validation..."

            # Wait for application to fully stabilize
            sleep 15

            # Comprehensive integration tests
            log "Running integration tests..."

            # Test 1: API Health
            if ! curl -f -s "http://localhost:5000/" > /dev/null; then
              error "API health check failed"
              echo "VALIDATION_FAILED: API health check failed"
              exit 1
            fi

            # Test 2: Database connectivity
            cd /root/fes-crm-backend
            if ! sudo docker compose -f docker-compose.prod.yml exec -T backend npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then
              error "Database connectivity test failed"
              echo "VALIDATION_FAILED: Database connectivity test failed"
              exit 1
            fi

            # Test 3: Container health
            if ! sudo docker compose -f docker-compose.prod.yml ps | grep -q "healthy\|Up"; then
              error "Container health check failed"
              echo "VALIDATION_FAILED: Container health check failed"
              exit 1
            fi

            # Test 4: Memory and CPU usage
            MEMORY_USAGE=$(sudo docker stats --no-stream --format "{{.MemPerc}}" fes_crm_prod | sed 's/%//' 2>/dev/null || echo "0")
            if [ -n "$MEMORY_USAGE" ] && (( $(echo "$MEMORY_USAGE > 90" | bc -l 2>/dev/null || echo "0") )); then
              echo "VALIDATION_WARNING: High memory usage detected: ${MEMORY_USAGE}%"
            fi

            log "All post-deployment validations passed"
            echo "VALIDATION_PASSED=true"

      - name: Set validation result
        id: validation-result
        run: |
          if [ "${{ steps.validation.outcome }}" == "success" ]; then
            echo "VALIDATION_PASSED=true" >> $GITHUB_OUTPUT
          else
            echo "VALIDATION_PASSED=false" >> $GITHUB_OUTPUT
          fi

      - name: Set deployment result
        id: deployment-result
        run: |
          if [ "${{ steps.deploy.outcome }}" == "success" ] && [ "${{ steps.validation.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

  rollback:
    name: Automatic Rollback
    runs-on: ubuntu-latest
    needs: deploy
    if: failure() && needs.deploy.outputs.deployment-status == 'failed'
    steps:
      - name: Rollback to previous version
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            log() { echo -e "\033[0;33m[$(date +'%Y-%m-%d %H:%M:%S')] ROLLBACK: $1\033[0m"; }
            error() { echo -e "\033[0;31m[$(date +'%Y-%m-%d %H:%M:%S')] ROLLBACK ERROR: $1\033[0m"; }

            log "Starting automatic rollback procedure..."

            # Find latest backup
            LATEST_BACKUP=$(find /root -name "fes-crm-backend-backup-*" -type d | sort | tail -1)

            if [ -z "$LATEST_BACKUP" ]; then
              error "No backup found for rollback"
              exit 1
            fi

            log "Rolling back to: $LATEST_BACKUP"

            # Stop current deployment
            cd /root/fes-crm-backend

            # Backup current .env before rollback
            if [ -f ".env" ]; then
              cp ".env" "/tmp/.env.rollback.backup"
              log "Current .env backed up before rollback"
            fi

            sudo docker compose -f docker-compose.prod.yml down --remove-orphans

            # Restore from backup
            cd /root
            rm -rf fes-crm-backend
            cp -r "$LATEST_BACKUP" fes-crm-backend

            # Restore .env file after rollback
            if [ -f "/tmp/.env.rollback.backup" ]; then
              cp "/tmp/.env.rollback.backup" "fes-crm-backend/.env"
              chown root:root "fes-crm-backend/.env"
              chmod 600 "fes-crm-backend/.env"
              log "Environment file restored after rollback"
            elif [ -f "$LATEST_BACKUP/.env" ]; then
              log "Using .env from backup (current .env not available)"
            else
              error "No .env file available after rollback"
              exit 1
            fi

            # Start restored version
            cd fes-crm-backend
            sudo docker compose -f docker-compose.prod.yml up -d

            # Health check after rollback
            sleep 30
            for i in {1..10}; do
              if curl -f -s "http://localhost:5000/" > /dev/null; then
                log "Rollback successful - application is healthy"
                exit 0
              fi
              sleep 10
            done

            error "Rollback failed - application not responding"
            exit 1

  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [ deploy, rollback ]
    if: always()
    steps:
      - name: Notify deployment result
        if: env.SLACK_WEBHOOK != ''
        run: |
          if [ "${{ needs.deploy.outputs.deployment-status }}" == "success" ]; then
            STATUS_EMOJI="✅"
            STATUS_TEXT="SUCCESS"
            COLOR="good"
          else
            STATUS_EMOJI="❌"
            STATUS_TEXT="FAILED"
            COLOR="danger"
          fi

          if [ "${{ needs.rollback.result }}" == "success" ]; then
            ROLLBACK_TEXT="\n🔄 Automatic rollback completed successfully"
          elif [ "${{ needs.rollback.result }}" == "failure" ]; then
            ROLLBACK_TEXT="\n💥 Automatic rollback failed - manual intervention required"
          else
            ROLLBACK_TEXT=""
          fi

          curl -X POST -H 'Content-type: application/json' \
            --data "{
              \"attachments\": [{
                \"color\": \"$COLOR\",
                \"title\": \"$STATUS_EMOJI FES CRM Backend Deployment $STATUS_TEXT\",
                \"fields\": [
                  {\"title\": \"Commit\", \"value\": \"${{ github.sha }}\", \"short\": true},
                  {\"title\": \"Branch\", \"value\": \"${{ github.ref_name }}\", \"short\": true},
                  {\"title\": \"Actor\", \"value\": \"${{ github.actor }}\", \"short\": true},
                  {\"title\": \"Environment\", \"value\": \"Production\", \"short\": true}
                ],
                \"text\": \"Deployment to production server completed.$ROLLBACK_TEXT\",
                \"footer\": \"GitHub Actions\",
                \"ts\": $(date +%s)
              }]
            }" \
            ${{ env.SLACK_WEBHOOK }}