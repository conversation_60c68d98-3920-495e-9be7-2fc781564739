generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum ConditionType {
  CONDITION
  COMPARISON
  ARITHMETIC
}

enum ActivityLogType {
  CREATE
  UPDATE
  DELETE
  FILE_UPLOAD
  STATUS_CHANGE
  NOTE_ADDED
  COMMENT
  
}

enum ApplicationSubStatus {
  FOLLOW_UP
  OFFER_RECEIVED
  DOCUMENT_PENDING
  INTERVIEW_SCHEDULED
  VISA_PENDING
  OTHER
}

enum ApplicantStatus {
  OFFER_ACCEPTED
  OFFER_DECLINED
  DECISION_PENDING
  FINAL_DECISION
  WITHDRAWN
  OFFER_ACCEPTED_OTHER_AGENT
  DEFER
  REAPPLY_LATER_INTAKE
}

enum StepType {
  CALCULATION
  ACTION
}

enum UserStatus {
  ACTIVE
  INACTIVE
  DELETED
}

enum VisaOutcome {
  PENDING
  APPROVED
  REJECTED
}

enum VisaType {
  TOURIST
  STUDY
  WORK
  SPOUSE
}

enum IeltsType {
  UKVI
  NON_UKVI
}

enum DegreeLevel {
  FOUNDATION
  BACHELORS
  MASTERS
}

enum StudentStatus {
  LEAD
  ACTIVE
  INACTIVE
  CASE_CLOSED
}

enum StudentSubStatus {
  // LEAD STATUS
  CALLED_ONCE_MESSAGE_SENT
  CALLED_TWICE_MESSAGE_SENT
  CALLED_THRICE_MESSAGE_SENT
  EMAIL_SENT
  CALLBACK
  POWERED_OFF
  NO_RESPONSE_AND_MESSAGE_SENT
  // ACTIVE STATUS
  FIRST_COUNSELLING
  SECOND_COUNSELLING
  DOCUMENTS_RECEIVED
  DOCUMENTS_EDITED
  MISSING_DOCUMENTS
  REGISTERED
  APPOINTMENT_SCHEDULED
  APPLICATION_SENT
  OFFER_RECEIVED
  APPLICATION_REJECTION
  VISA_REJECTED
  LATER_INTAKE
  COURSE_FULL
  WITHDRAWN
  // INACTIVE STATUS
  NO_RESPONSE
  NOT_INTERESTED
  NOT_SERIOUS
  CANT_AFFORD
  NOT_ELIGIBLE
  INCORRECT_CONTACT_INFO
  APPLICATION_WITHDRAWN
  // CASE CLOSED STATUS
  VISA_RECEIVED
  ENROLLED
  PAID_TUITION
  OFFER
  SELF_APPLY
  // OTHER STATUS
  OTHER_AGENT

}

enum ApplicationStatus {
  APPLIED
  AWAITING_RESPONSE
  ACCEPTED
  REJECTED
  VISA_APPLIED
  VISA_ACCEPTED
  VISA_REJECTED
  ENROLLED
  PAID_TUITION
  AWAITING_DOCUMENTS
  OFFER
}

enum LeadSource {
  SUB_AGENT
  FES_PORTAL
  MARKETING_LEAD
  MANAGEMENT
  SUB_AGENT_REFERRAL_LEAD
  SOCIAL_MEDIA
  STUDY_FAIR
  EXPO
  REFERRAL
  PERSONAL_REFERRAL
  WALK_IN
  TAGGING
  OTHER
}

enum Actors {
  ADMIN
  STUDENT
  COUNSELLOR
}

enum EventType {
  // CRUD Operations
  CREATE_STUDENT
  UPDATE_STUDENT
  DELETE_STUDENT
  RESTORE_STUDENT
  CREATE_COUNSELLOR
  UPDATE_COUNSELLOR
  DELETE_COUNSELLOR
  RESTORE_COUNSELLOR
  CREATE_UNIVERSITY
  UPDATE_UNIVERSITY
  DELETE_UNIVERSITY
  RESTORE_UNIVERSITY
  CREATE_PROGRAM
  UPDATE_PROGRAM
  DELETE_PROGRAM
  RESTORE_PROGRAM
  CREATE_DOCUMENT
  UPDATE_DOCUMENT
  DELETE_DOCUMENT
  RESTORE_DOCUMENT
  CREATE_APPLICATION
  UPDATE_APPLICATION
  DELETE_APPLICATION
  RESTORE_APPLICATION
  
  // User Actions
  USER_LOGIN
  USER_LOGOUT
  ROLE_CHANGE
  PERMISSION_UPDATE
  EXPORT_DATA
  
  // Document Actions
  FILE_UPLOAD
  FILE_DELETE
  
  // Status Changes
  APPLICATION_STATUS_CHANGE
  STUDENT_STATUS_CHANGE
  
  // Communication
  NOTE_ADDED
  COMMENT_ADDED
  EMAIL_SENT
  
  // System Events (for future v2)
  BULK_IMPORT
  BULK_UPDATE
  API_SYNC
  
  // Security Events (for future v2)
  FAILED_LOGIN
  SUSPICIOUS_ACCESS
  PASSWORD_CHANGE
  
  // Integration Events (for future v2)
  WEBHOOK_TRIGGER
  NOTIFICATION_SENT
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

model Actor {
  id        String @id @default(uuid())
  userId    String
  role      String
  ipAddress String
  deviceId  String
  event     Event?
}

model TargetEntity {
  id            String   @id @default(uuid())
  type          String
  entityId      String
  fieldModified String[]
  event         Event?
}

model Context {
  id               String @id @default(uuid())
  sessionId        String
  browserUserAgent String
  geolocation      String
  event            Event?
}

model Event {
  id        String   @id @default(uuid())
  timestamp DateTime @default(now())
  eventType String

  // Target Entity
  type          String
  entityId      String
  fieldModified String[]

  // Foreign keys (each must be unique to enforce one-to-one)
  actorId        String @unique
  targetEntityId String @unique
  contextId      String @unique

  // Context
  sessionId        String
  browserUserAgent String
  geolocation      String

  beforeSnapshot Json?
  afterSnapshot  Json?
  parentEventId  String?

  actor        Actor        @relation(fields: [actorId], references: [id])
  targetEntity TargetEntity @relation(fields: [targetEntityId], references: [id])
  context      Context      @relation(fields: [contextId], references: [id])

  parentEvent Event?  @relation("EventRelation", fields: [parentEventId], references: [id])
  childEvents Event[] @relation("EventRelation")
}

model Permission {
  id          String  @id @default(uuid())
  name        String  @unique // e.g., "manage:students", "view:reports"
  description String?
  roles       Role[]  @relation("RolePermissions")
  group       String? // e.g., "STUDENTS", "APPLICATIONS", "HR"
}

// Roles & Permissions (RBAC)
model Role {
  id          String       @id @default(uuid())
  name        String       @unique // e.g., COUNSELLOR, HR, MANAGER, OWNER
  description String?
  permissions Permission[] @relation("RolePermissions")
  users       User[]       @relation("UserRoles") // Employees assigned to this role
}

// Base user model for authentication
model User {
  id                String        @id @default(uuid())
  email             String        @unique
  name              String?
  password          String
  status            UserStatus    @default(ACTIVE)
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  roles             Role[]        @relation("UserRoles")
  approvedDocuments Document[]    @relation("DocumentApprover")
  // Relationships
  employee          Employee?     @relation("UserEmployee")
  student           Student?      @relation("UserStudent")
  agent             Agent?        @relation("UserAgent")
  activityLogs      ActivityLog[]
}

// Employee model for all internal staff (counsellors, HR, managers, etc.)
model Employee {
  id        String    @id @default(uuid())
  firstName String
  lastName  String
  phone     String
  userId    String    @unique
  user      User      @relation("UserEmployee", fields: [userId], references: [id])
  //roleId      String        // Role determines permissions (e.g., COUNSELLOR, HR, MANAGER)
  //role        Role          @relation(fields: [roleId], references: [id])
  officeId  String? // Office/city/region/country hierarchy (see below)
  office    Office?   @relation(fields: [officeId], references: [id])
  students  Student[] @relation("EmployeeStudents") // Counsellors manage students
  applications Application[] @relation("EmployeeStudents") // Applications managed by this employee
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  Country   Country?  @relation(fields: [countryId], references: [id])
  countryId String?
  Region    Region?   @relation(fields: [regionId], references: [id])
  regionId  String?
  City      City?     @relation(fields: [cityId], references: [id])
  cityId    String?
}

model Qualification {
  id             String   @id @default(uuid())
  qualification  String
  specialization String
  passingYear    DateTime @db.Date
  obtainedMarks  Float
  totalMarks     Float
  studentId      String? // Optional foreign key to Student
  student        Student? @relation(fields: [studentId], references: [id])
}

model Visa {
  id            String      @id @default(uuid())
  type          VisaType
  outcome       VisaOutcome
  country       String
  yearOfRefusal DateTime?   @db.Date()
  student       Student     @relation(fields: [studentId], references: [id])
  studentId     String
}

// Student model (clients)
model Student {
  id                      String                @id @default(uuid())
  userId                  String                @unique
  user                    User                  @relation("UserStudent", fields: [userId], references: [id])
  officeId                String?
  office                  Office?               @relation(fields: [officeId], references: [id])
  firstName               String
  lastName                String
  CNIC                    String                @default("")
  address                 String                @default("")
  city                    String
  dateOfBirth             DateTime              @db.Date
  cellNo                  String                @default("")
  phoneNo                 String?
  leadDate                DateTime?             @db.Date
  nationality             String                @default("")
  passport                Boolean               @default(false)
  qualifications          Qualification[]
  workExperience          Float?
  ieltsScore              Float?
  ieltsYear               DateTime?             @db.Date
  ieltsType               IeltsType?
  visas                   Visa[]
  interestedFields        String[]
  interestedCountries     String[]
  leadSource              LeadSource            @default(OTHER)
  leadSourceName          String?
  notes                   String?
  counsellorId            String?
  counsellor              Employee?             @relation("EmployeeStudents", fields: [counsellorId], references: [id])
  applications            Application[]         @relation("StudentApplications")
  agentId                 String?
  agent                   Agent?                @relation(fields: [agentId], references: [id])
  lastInstituteAttended   String
  lastInstituteDegree     String
  interestedDegreeLevel   DegreeLevel
  referralCode            String?
  status                  StudentStatus         @default(LEAD)
  subStatus               StudentSubStatus      @default(EMAIL_SENT)
  documents               Document[]
 
  createdAt               DateTime              @default(now())
  updatedAt               DateTime              @updatedAt
}

// University application workflow
model Application {
  id                    String                  @id @default(uuid())
  studentId             String
  student               Student                 @relation("StudentApplications", fields: [studentId], references: [id])
  universityId          String
  university            University              @relation(fields: [universityId], references: [id])
  status                ApplicationStatus       @default(APPLIED)
  subStatus            ApplicationSubStatus?
  applicantStatus      ApplicantStatus?
  appliedDate           DateTime?
  decisionDate          DateTime?
  visaAppliedDate       DateTime?
  visaDecisionDate      DateTime?
  programId             String
  program               Program                 @relation(fields: [programId], references: [id])
  notes                 String?
  counsellorId          String?
  counsellor            Employee?              @relation("EmployeeStudents", fields: [counsellorId], references: [id])
  agentId               String?
  agent                 Agent?                 @relation(fields: [agentId], references: [id])
  lastInstituteAttended String
  lastInstituteDegree   String
  interestedDegreeLevel DegreeLevel
  documents             Document[]
  activityLogs          ActivityLog[]
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
}

model Contact {
  id           String     @id @default(uuid())
  universityId String
  university   University @relation(fields: [universityId], references: [id], onDelete: Cascade)

  name        String
  email       String   @unique
  phone       String?
  designation String?
  department  String?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Universities
model University {
  id           String        @id @default(uuid())
  name         String        @unique
  description  String?
  location     String        @default("")
  ranking      Int?
  programs     Program[]
  contacts     Contact[]
  applications Application[]
  //scholarships      Scholarship[]       // Relation to scholarships offered by the university
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

model Program {
  id                  String        @id @default(uuid())
  name                String
  duration            String
  degreeType          String
  tuitionFees         Float
  applicationDeadline DateTime
  university          University    @relation(fields: [universityId], references: [id])
  universityId        String
  applications        Application[]
  //scholarships            Scholarship[]       // Relation to scholarships available for the program
}

// Extend Document Model
model Document {
  id              String          @id @default(uuid())
  type            String
  fileUrl         String
  studentId       String?         // Foreign key field
  student         Student?        @relation(fields: [studentId], references: [id])
  applicationId   String?         // Application file support
  application     Application?    @relation(fields: [applicationId], references: [id])
  status          DocumentStatus  @default(PENDING)
  rejectionReason String?
  approvedBy      User?           @relation("DocumentApprover", fields: [approvedById], references: [id])
  approvedById    String?
  approvedAt      DateTime?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  comment         String?
}

// NEW Model for Notes/Activity Logs
model ActivityLog {
  id            String      @id @default(uuid())
  application   Application @relation(fields: [applicationId], references: [id])
  applicationId String
  content       String
  createdAt     DateTime    @default(now())
  createdBy     User        @relation(fields: [createdById], references: [id])
  createdById   String
}

// Organizational hierarchy (Country > Region > City > Office)
model Country {
  id        String     @id @default(uuid())
  name      String     @unique
  regions   Region[]
  offices   Office[]
  employees Employee[] // Country-level managers
}

model Region {
  id        String     @id @default(uuid())
  name      String     @unique
  countryId String
  country   Country    @relation(fields: [countryId], references: [id])
  cities    City[]
  offices   Office[]
  employees Employee[] // Region-level managers
}

model City {
  id        String     @id @default(uuid())
  name      String     @unique
  regionId  String
  region    Region     @relation(fields: [regionId], references: [id])
  offices   Office[]
  employees Employee[] // City-level managers
}

model Office {
  id        String     @id @default(uuid())
  name      String     @unique
  cityId    String
  city      City       @relation(fields: [cityId], references: [id])
  students  Student[] // Relation to Students
  employees Employee[] // Office staff (counsellors, HR, etc.)
  Country   Country?   @relation(fields: [countryId], references: [id])
  countryId String?
  Region    Region?    @relation(fields: [regionId], references: [id])
  regionId  String?
}

// Agents (external referrers)
model Agent {
  id         String    @id @default(uuid())
  userId     String    @unique
  user       User      @relation("UserAgent", fields: [userId], references: [id])
  commission Float? // Commission rate/amount
  students   Student[] // Students referred by this agent
  applications Application[] // Applications referred by this agent
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}

model Workflow {
  id          String    @id @default(uuid())
  name        String
  description String?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  active      Boolean   @default(true)
  //actions           Action[]
  //conditions        Condition[]
  steps       Step[]
  triggers    Trigger[]
}

model Step {
  id          String     @id @default(uuid())
  name        String //  Testing purposes
  type        StepType // 'calculation' or 'action'
  details     Json
  order       Int
  workflow_id String
  workflow    Workflow   @relation(fields: [workflow_id], references: [id])
  conditionId String?
  condition   Condition? @relation(fields: [conditionId], references: [id])

  // Optional relations for conditional steps:
  ifConditionId   String?
  ifCondition     Condition? @relation("IfBranch", fields: [ifConditionId], references: [id])
  elseConditionId String?
  elseCondition   Condition? @relation("ElseBranch", fields: [elseConditionId], references: [id])

  actionId String?
  action   Action? @relation(fields: [actionId], references: [id])
}

model Trigger {
  id          String    @id @default(uuid())
  event_type  EventType // Triggers regardless of who caused the event
  actor       Actors? // If want to trigger only if event happened by specific Actor
  workflow_id String
  workflow    Workflow  @relation(fields: [workflow_id], references: [id])
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
}

model Condition {
  id         String        @id @default(uuid())
  type       ConditionType
  leftValue  Json          @default("{}") // Left value for comparison or operation
  rightValue Json          @default("{}") // Right value for comparison or operation
  operator   String // Operator for comparison or operation
  criteria   Json?
  ifBranch   Step[]        @relation("IfBranch")
  elseBranch Step[]        @relation("ElseBranch")
  steps      Step[]
  created_at DateTime      @default(now())
  updated_at DateTime      @updatedAt
}

model Action {
  id         String   @id @default(uuid())
  type       String // maybe store the Action name here directly
  details    Json
  steps      Step[]
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}
