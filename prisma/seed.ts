import { PrismaClient } from '@prisma/client';
import { actors } from './seeds/actors';
import { applications } from './seeds/applications';
import { cities } from './seeds/cities';
import { contacts } from './seeds/contacts';
import { contexts } from './seeds/contexts';
import { countries } from './seeds/countries';
import { documents } from './seeds/documents';
import { employees } from './seeds/employees';
import { events } from './seeds/events';
import { offices } from './seeds/offices';
import { permissions } from './seeds/permissions';
import { programs } from './seeds/programs';
import { qualifications } from './seeds/qualifications';
import { regions } from './seeds/regions';
import { roles } from './seeds/roles';
import { students } from './seeds/students';
import { targetEntities } from './seeds/targetEntities';
import { universities } from './seeds/universities';
import { users } from './seeds/users';
import { visas } from './seeds/visas';

const prisma = new PrismaClient();

async function main() {
  const seedData = [
    { model: 'permission', data: permissions },
    { model: 'role', data: roles },
    { model: 'user', data: users },
    { model: 'country', data: countries },
    { model: 'region', data: regions },
    { model: 'city', data: cities },
    { model: 'office', data: offices },
    { model: 'employee', data: employees },
    { model: 'student', data: students },
    { model: 'qualification', data: qualifications },
    { model: 'visa', data: visas },
    { model: 'university', data: universities },
    { model: 'contact', data: contacts },
    { model: 'program', data: programs },
    { model: 'document', data: documents },
    { model: 'application', data: applications },
    { model: 'actor', data: actors },
    { model: 'targetEntity', data: targetEntities },
    { model: 'context', data: contexts },
    { model: 'event', data: events },
  ];

  for (const { model, data } of seedData) {
    for (const item of data) {
      await prisma[model].upsert({
        where: { id: item.id },
        create: item,
        update: item,
      });
    }
    console.log(`${model}s created!`);
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
