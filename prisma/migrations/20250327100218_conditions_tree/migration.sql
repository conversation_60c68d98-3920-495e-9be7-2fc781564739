/*
  Warnings:

  - You are about to drop the column `conditionId` on the `Step` table. All the data in the column will be lost.

*/
-- DropFore<PERSON>Key
ALTER TABLE "Step" DROP CONSTRAINT "Step_conditionId_fkey";

-- AlterTable
ALTER TABLE "Step" DROP COLUMN "conditionId",
ADD COLUMN     "elseConditionId" TEXT,
ADD COLUMN     "ifConditionId" TEXT;

-- AddForeignKey
ALTER TABLE "Step" ADD CONSTRAINT "Step_ifConditionId_fkey" FOREIGN KEY ("ifConditionId") REFERENCES "Condition"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON><PERSON>Key
ALTER TABLE "Step" ADD CONSTRAINT "Step_elseConditionId_fkey" FOREIGN KEY ("elseConditionId") REFERENCES "Condition"("id") ON DELETE SET NULL ON UPDATE CASCADE;
