-- CreateEnum
CREATE TYPE "ApplicationSubStatus" AS ENUM ('FOLLOW_UP', 'OFFER_RECEIVED', 'DOCUMENT_PENDING', 'INTERVIEW_SCHEDULED', 'VISA_PENDING', 'OTHER');

-- AlterTable
ALTER TABLE "Application" ADD COLUMN     "subStatus" "ApplicationSubStatus";

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "applicationId" TEXT,
ALTER COLUMN "updatedAt" DROP DEFAULT;

-- CreateTable
CREATE TABLE "ActivityLog" (
    "id" TEXT NOT NULL,
    "applicationId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT NOT NULL,

    CONSTRAINT "ActivityLog_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "Application"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActivityLog" ADD CONSTRAINT "ActivityLog_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "Application"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
