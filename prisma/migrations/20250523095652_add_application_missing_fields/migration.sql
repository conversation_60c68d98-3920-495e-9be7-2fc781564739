/*
  Warnings:

  - The values [OFFER] on the enum `StudentStatus` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `interestedDegreeLevel` to the `Application` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastInstituteAttended` to the `Application` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastInstituteDegree` to the `Application` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "EventType" ADD VALUE 'DELETE_STUDENT';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_STUDENT';
ALTER TYPE "EventType" ADD VALUE 'DELETE_COUNSELLOR';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_COUNSELLOR';
ALTER TYPE "EventType" ADD VALUE 'DELETE_UNIVERSITY';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_UNIVERSITY';
ALTER TYPE "EventType" ADD VALUE 'DELETE_PROGRAM';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_PROGRAM';
ALTER TYPE "EventType" ADD VALUE 'DELETE_DOCUMENT';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_DOCUMENT';
ALTER TYPE "EventType" ADD VALUE 'CREATE_APPLICATION';
ALTER TYPE "EventType" ADD VALUE 'UPDATE_APPLICATION';
ALTER TYPE "EventType" ADD VALUE 'DELETE_APPLICATION';
ALTER TYPE "EventType" ADD VALUE 'RESTORE_APPLICATION';
ALTER TYPE "EventType" ADD VALUE 'USER_LOGIN';
ALTER TYPE "EventType" ADD VALUE 'USER_LOGOUT';
ALTER TYPE "EventType" ADD VALUE 'ROLE_CHANGE';
ALTER TYPE "EventType" ADD VALUE 'PERMISSION_UPDATE';
ALTER TYPE "EventType" ADD VALUE 'EXPORT_DATA';
ALTER TYPE "EventType" ADD VALUE 'FILE_UPLOAD';
ALTER TYPE "EventType" ADD VALUE 'FILE_DELETE';
ALTER TYPE "EventType" ADD VALUE 'APPLICATION_STATUS_CHANGE';
ALTER TYPE "EventType" ADD VALUE 'STUDENT_STATUS_CHANGE';
ALTER TYPE "EventType" ADD VALUE 'NOTE_ADDED';
ALTER TYPE "EventType" ADD VALUE 'COMMENT_ADDED';
ALTER TYPE "EventType" ADD VALUE 'EMAIL_SENT';
ALTER TYPE "EventType" ADD VALUE 'BULK_IMPORT';
ALTER TYPE "EventType" ADD VALUE 'BULK_UPDATE';
ALTER TYPE "EventType" ADD VALUE 'API_SYNC';
ALTER TYPE "EventType" ADD VALUE 'FAILED_LOGIN';
ALTER TYPE "EventType" ADD VALUE 'SUSPICIOUS_ACCESS';
ALTER TYPE "EventType" ADD VALUE 'PASSWORD_CHANGE';
ALTER TYPE "EventType" ADD VALUE 'WEBHOOK_TRIGGER';
ALTER TYPE "EventType" ADD VALUE 'NOTIFICATION_SENT';

-- AlterEnum
BEGIN;
CREATE TYPE "StudentStatus_new" AS ENUM ('LEAD', 'ACTIVE', 'INACTIVE', 'CASE_CLOSED');
ALTER TABLE "Student" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Student" ALTER COLUMN "status" TYPE "StudentStatus_new" USING ("status"::text::"StudentStatus_new");
ALTER TYPE "StudentStatus" RENAME TO "StudentStatus_old";
ALTER TYPE "StudentStatus_new" RENAME TO "StudentStatus";
DROP TYPE "StudentStatus_old";
ALTER TABLE "Student" ALTER COLUMN "status" SET DEFAULT 'LEAD';
COMMIT;

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "StudentSubStatus" ADD VALUE 'PAID_TUITION';
ALTER TYPE "StudentSubStatus" ADD VALUE 'OFFER';

-- AlterTable
ALTER TABLE "Application" ADD COLUMN     "agentId" TEXT,
ADD COLUMN     "counsellorId" TEXT,
ADD COLUMN     "interestedDegreeLevel" "DegreeLevel" NOT NULL,
ADD COLUMN     "lastInstituteAttended" TEXT NOT NULL,
ADD COLUMN     "lastInstituteDegree" TEXT NOT NULL,
ADD COLUMN     "referralCode" TEXT;

-- AddForeignKey
ALTER TABLE "Application" ADD CONSTRAINT "Application_counsellorId_fkey" FOREIGN KEY ("counsellorId") REFERENCES "Employee"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Application" ADD CONSTRAINT "Application_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;
