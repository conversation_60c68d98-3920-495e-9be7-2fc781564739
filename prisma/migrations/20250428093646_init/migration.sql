/*
  Warnings:

  - The values [PENDING,VISA_APPLIED,VISA_ACCEPTED,VISA_REJECTED,ENROLLED,PAID_TUITION] on the enum `StudentStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [FOLLOW_UP,SENT_EMAIL,PENDING] on the enum `StudentSubStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "StudentStatus_new" AS ENUM ('LEAD', 'ACTIVE', 'INACTIVE', 'CASE_CLOSED');
ALTER TABLE "Student" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Student" ALTER COLUMN "status" TYPE "StudentStatus_new" USING ("status"::text::"StudentStatus_new");
ALTER TYPE "StudentStatus" RENAME TO "StudentStatus_old";
ALTER TYPE "StudentStatus_new" RENAME TO "StudentStatus";
DROP TYPE "StudentStatus_old";
ALTER TABLE "Student" ALTER COLUMN "status" SET DEFAULT 'LEAD';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "StudentSubStatus_new" AS ENUM ('CALLED_ONCE_MESSAGE_SENT', 'CALLED_TWICE_MESSAGE_SENT', 'CALLED_THRICE_MESSAGE_SENT', 'EMAIL_SENT', 'CALLBACK', 'POWERED_OFF', 'NO_RESPONSE_AND_MESSAGE_SENT', 'FIRST_COUNSELLING', 'SECOND_COUNSELLING', 'DOCUMENTS_RECEIVED', 'DOCUMENTS_EDITED', 'MISSING_DOCUMENTS', 'REGISTERED', 'APPOINTMENT_SCHEDULED', 'APPLICATION_SENT', 'OFFER_RECEIVED', 'APPLICATION_REJECTION', 'VISA_REJECTED', 'LATER_INTAKE', 'COURSE_FULL', 'WITHDRAWN', 'NO_RESPONSE', 'NOT_INTERESTED', 'NOT_SERIOUS', 'CANT_AFFORD', 'NOT_ELIGIBLE', 'INCORRECT_CONTACT_INFO', 'APPLICATION_WITHDRAWN', 'VISA_RECEIVED', 'ENROLLED', 'SELF_APPLY', 'OTHER_AGENT');
ALTER TABLE "Student" ALTER COLUMN "subStatus" DROP DEFAULT;
ALTER TABLE "Student" ALTER COLUMN "subStatus" TYPE "StudentSubStatus_new" USING ("subStatus"::text::"StudentSubStatus_new");
ALTER TYPE "StudentSubStatus" RENAME TO "StudentSubStatus_old";
ALTER TYPE "StudentSubStatus_new" RENAME TO "StudentSubStatus";
DROP TYPE "StudentSubStatus_old";
ALTER TABLE "Student" ALTER COLUMN "subStatus" SET DEFAULT 'EMAIL_SENT';
COMMIT;

-- AlterTable
ALTER TABLE "Student" ALTER COLUMN "status" SET DEFAULT 'LEAD',
ALTER COLUMN "subStatus" SET DEFAULT 'EMAIL_SENT';
