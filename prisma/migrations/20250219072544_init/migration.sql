/*
  Warnings:

  - Added the required column `city` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `firstName` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `interestedDegreeLevel` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastInstituteAttended` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastInstituteDegree` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastName` to the `Student` table without a default value. This is not possible if the table is not empty.
  - Added the required column `phone` to the `Student` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "DegreeLevel" AS ENUM ('FOUNDATION', 'BACHELORS', 'MASTERS');

-- AlterTable
ALTER TABLE "Student"
ADD COLUMN     "city" TEXT NOT NULL,
ADD COLUMN     "firstName" TEXT NOT NULL,
ADD COLUMN     "interestedCountries" TEXT[],
ADD COLUMN     "interestedDegreeLevel" "DegreeLevel" NOT NULL,
ADD COLUMN     "interestedFields" TEXT[],
ADD COLUMN     "lastInstituteAttended" TEXT NOT NULL,
ADD COLUMN     "lastInstituteDegree" TEXT NOT NULL,
ADD COLUMN     "lastName" TEXT NOT NULL,
ADD COLUMN     "phone" TEXT NOT NULL,
ADD COLUMN     "referralCode" TEXT;
