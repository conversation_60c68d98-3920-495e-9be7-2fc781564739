/*
  Warnings:

  - Added the required column `dateOfBirth` to the `Student` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "VisaOutcome" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "VisaType" AS ENUM ('TOURIST', 'STUDY', 'WORK', 'SPOUSE');

-- CreateEnum
CREATE TYPE "StudentStatus" AS ENUM ('PENDING', 'VISA_APPLIED', 'VISA_ACCEPTED', 'VISA_REJECTED', 'ENROLLED', 'PAID_TUITION');

-- AlterTable
ALTER TABLE "Student" ADD COLUMN     "dateOfBirth" DATE NOT NULL,
ADD COLUMN     "status" "StudentStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "workExperience" DOUBLE PRECISION;

-- CreateTable
CREATE TABLE "Visa" (
    "id" TEXT NOT NULL,
    "type" "VisaType" NOT NULL,
    "outcome" "VisaOutcome" NOT NULL,
    "country" TEXT NOT NULL,
    "yearOfRefusal" DATE,
    "studentId" TEXT NOT NULL,

    CONSTRAINT "Visa_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Visa" ADD CONSTRAINT "Visa_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
