/*
  Warnings:

  - The values [STUDY_<PERSON>XPO,UNIVERSITY_AMBASSADOR,AGENT] on the enum `LeadSource` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LeadSource_new" AS ENUM ('SUB_AGENT', 'FES_PORTAL', 'MARKETING_LEAD', 'MANAGEMENT', 'SUB_AGENT_REFERRAL_LEAD', 'SOCIAL_MEDIA', 'STUDY_FAIR', 'EXPO', 'REFERRAL', 'PERSONAL_REFERRAL', 'WALK_IN', 'TAGGING', 'OTHER');
ALTER TABLE "Student" ALTER COLUMN "leadSource" DROP DEFAULT;
ALTER TABLE "Student" ALTER COLUMN "leadSource" TYPE "LeadSource_new" USING ("leadSource"::text::"LeadSource_new");
ALTER TYPE "LeadSource" RENAME TO "LeadSource_old";
ALTER TYPE "LeadSource_new" RENAME TO "LeadSource";
DROP TYPE "LeadSource_old";
ALTER TABLE "Student" ALTER COLUMN "leadSource" SET DEFAULT 'OTHER';
COMMIT;
