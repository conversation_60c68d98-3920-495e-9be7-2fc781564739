/*
  Warnings:

  - You are about to drop the column `phone` on the `Student` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "IeltsType" AS ENUM ('UKVI', 'NON_UKVI');

-- AlterTable
ALTER TABLE "Student" DROP COLUMN "phone",
ADD COLUMN     "CNIC" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "address" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "cellNo" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "ieltsScore" DOUBLE PRECISION,
ADD COLUMN     "ieltsType" "IeltsType",
ADD COLUMN     "ieltsYear" DATE,
ADD COLUMN     "leadDate" DATE,
ADD COLUMN     "nationality" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "passport" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "officeId" TEXT,
ADD COLUMN     "phoneNo" TEXT;

-- CreateTable
CREATE TABLE "Qualification" (
    "id" TEXT NOT NULL,
    "qualification" TEXT NOT NULL,
    "specialization" TEXT NOT NULL,
    "passingYear" DATE NOT NULL,
    "obtainedMarks" DOUBLE PRECISION NOT NULL,
    "totalMarks" DOUBLE PRECISION NOT NULL,
    "studentId" TEXT,

    CONSTRAINT "Qualification_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Qualification" ADD CONSTRAINT "Qualification_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Student" ADD CONSTRAINT "Student_officeId_fkey" FOREIGN KEY ("officeId") REFERENCES "Office"("id") ON DELETE SET NULL ON UPDATE CASCADE;
