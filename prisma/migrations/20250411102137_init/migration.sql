-- CreateTable
CREATE TABLE "_StudentApplications" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_StudentApplications_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_StudentApplications_B_index" ON "_StudentApplications"("B");

-- AddForeignKey
ALTER TABLE "_StudentApplications" ADD CONSTRAINT "_StudentApplications_A_fkey" FOREIGN KEY ("A") REFERENCES "Application"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_StudentApplications" ADD CONSTRAINT "_StudentApplications_B_fkey" FOREIGN KEY ("B") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;
