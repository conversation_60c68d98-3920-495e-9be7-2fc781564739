/*
  Warnings:

  - Changed the type of `type` on the `Condition` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "ConditionType" AS ENUM ('CONDITION', 'COMPARISON', 'ARITHMETIC');

-- CreateEnum
CREATE TYPE "StepType" AS ENUM ('CALCULATION', 'ACTION');

-- AlterTable
ALTER TABLE "Condition" DROP COLUMN "type",
ADD COLUMN     "type" "ConditionType" NOT NULL;

-- DropEnum
DROP TYPE "Calculation";
