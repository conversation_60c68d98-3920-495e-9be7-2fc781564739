/*
  Warnings:

  - You are about to drop the column `createdBy` on the `ActivityLog` table. All the data in the column will be lost.
  - You are about to drop the column `referralCode` on the `Application` table. All the data in the column will be lost.
  - Added the required column `createdById` to the `ActivityLog` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "ActivityLog" DROP COLUMN "createdBy",
ADD COLUMN     "createdById" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Application" DROP COLUMN "referralCode";

-- AddForeignKey
ALTER TABLE "ActivityLog" ADD CONSTRAINT "ActivityLog_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
