import { Prisma, StudentStatus } from '@prisma/client';

export const students: Prisma.StudentCreateInput[] = [
  {
    id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
    status: StudentStatus.LEAD,
    firstName: 'John',
    lastName: 'Doe',
    CNIC: '12345-6789012-3',
    cellNo: '*********',
    dateOfBirth: new Date('2000-05-15'),
    workExperience: 1.5,
    phoneNo: '0421234567',
    address: '123 Main St, Lahore',
    nationality: 'Pakistani',
    passport: true,
    city: 'Lahore',
    ieltsScore: 7.5,
    ieltsYear: new Date('2023-05-15'),
    ieltsType: 'NON_UKVI',
    lastInstituteAttended: 'FAST-NUCES',
    lastInstituteDegree: 'BSCS',
    interestedDegreeLevel: 'MASTERS',
    interestedFields: ['Computer Science'],
    interestedCountries: ['Canada'],
    referralCode: 'REF123',
    notes: 'Interested in AI specialization.',
    leadDate: new Date('2023-06-01'),
    leadSource: 'SOCIAL_MEDIA',
    user: {
      connect: {
        id: 'c3d4e5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r8',
      },
    },
    counsellor: {
      connect: {
        id: 'h8i9j0k1-l2m3-4n5o-6p7q-8r9stuvwxyz',
      },
    },
    office: {
      connect: {
        id: 'g7h8i9j0-k1l2-3m4n-5o6p-7q8r9stuvwx',
      },
    },
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
    status: StudentStatus.LEAD,
    firstName: 'Jane',
    lastName: 'Smith',
    CNIC: '23456-7890123-4',
    cellNo: '*********',
    phoneNo: '0421234568',
    address: '456 Market St, Lahore',
    nationality: 'Pakistani',
    passport: true,
    dateOfBirth: new Date('1997-05-15'),
    workExperience: 3,
    city: 'Lahore',
    ieltsScore: 8.0,
    ieltsYear: new Date('2022-08-20'),
    ieltsType: 'UKVI',
    lastInstituteAttended: 'PUCIT',
    lastInstituteDegree: 'BSIT',
    interestedDegreeLevel: 'MASTERS',
    interestedFields: ['Information Technology'],
    interestedCountries: ['Australia'],
    referralCode: 'REF456',
    notes: 'Looking for scholarship opportunities.',
    leadDate: new Date('2023-07-10'),
    leadSource: 'WALK_IN',
    user: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p',
      },
    },
    counsellor: {
      connect: {
        id: 'h8i9j0k1-l2m3-4n5o-6p7q-8r9stuvwxyz',
      },
    },
    office: {
      connect: {
        id: 'h1i2j3k4-l5m6-7n8o-9p0q-r1s2t3u4v5w6',
      },
    },
  },
];
