import { Prisma } from '@prisma/client';

export const targetEntities: Prisma.TargetEntityCreateInput[] = [
  {
    id: 't1u2v3w4-x5y6-z7a8-b9c0-d1e2f3g4h5i6',
    type: 'contact',
    entityId: 'contact_123',
    fieldModified: ['email'],
  },
  {
    id: 'u2v3w4x5-y6z7-a8b9-c0d1-e2f3g4h5i6j7',
    type: 'user',
    entityId: 'user_456',
    fieldModified: [],
  },
  {
    id: 'v3w4x5y6-z7a8-b9c0-d1e2-f3g4h5i6j7k8',
    type: 'contact',
    entityId: 'contact_789',
    fieldModified: [],
  },
  {
    id: 'w4x5y6z7-a8b9-c0d1-e2f3-g4h5i6j7k8l9',
    type: 'contact',
    entityId: 'contact_101',
    fieldModified: ['status'],
  },
  {
    id: 'x5y6z7a8-b9c0-d1e2-f3g4-h5i6j7k8l9m0',
    type: 'contact',
    entityId: 'contact_bulk',
    fieldModified: ['status', 'email'],
  },
];
