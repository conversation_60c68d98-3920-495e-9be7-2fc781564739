import { Prisma } from '@prisma/client';

export const actors: Prisma.ActorCreateInput[] = [
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7q8r9st',
    userId: 'user-uuid-1',
    role: 'admin',
    ipAddress: '***********',
    deviceId: 'mobile-xyz',
  },
  {
    id: 'f6g7h8i9-j0k1-2l3m-4n5o-6p7q8r9stuv',
    userId: 'user-uuid-2',
    role: 'user',
    ipAddress: '***********',
    deviceId: 'desktop-abc',
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
    userId: 'user-uuid-3',
    role: 'admin',
    ipAddress: '***********',
    deviceId: 'mobile-abc',
  },
  {
    id: 'p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1',
    userId: 'user-uuid-4',
    role: 'admin',
    ipAddress: '***********',
    deviceId: 'tablet-xyz',
  },
  {
    id: '12345678-90ab-cdef-1234-567890abcdef',
    userId: 'user-uuid-5',
    role: 'admin',
    ipAddress: '***********',
    deviceId: 'desktop-xyz',
  },
];
