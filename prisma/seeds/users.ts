import { Prisma } from '@prisma/client';

export const users: Prisma.UserCreateInput[] = [
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
    email: '<EMAIL>',
    name: 'Admin User',
    password: '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe', //password
    status: 'ACTIVE',
    roles: {
      connect: {
        id: '9f4c8e7d-12a3-4d9e-8c7d-4f0a4e5e8d3f',
      },
    },
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
    email: '<EMAIL>',
    password: '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe', //password
    name: '<PERSON>',
    status: 'ACTIVE',
    roles: {
      connect: {
        id: 'c7f8e9d0-2b4e-489d-b7e3-4d2c8f7d9f0b',
      },
    },
  },
  {
    id: 'c3d4e5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r8',
    email: '<EMAIL>',
    name: 'Jane Doe',
    password: '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe', //password
    status: 'ACTIVE',
    roles: {
      connect: {
        id: 'b5c6d7e8-f9g0-1h2i-3j4k-l5m6n7o8p9q0',
      },
    },
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p',
    email: '<EMAIL>',
    name: 'Student Second',
    password: '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe', //password
    status: 'ACTIVE',
    roles: {
      connect: {
        id: 'b5c6d7e8-f9g0-1h2i-3j4k-l5m6n7o8p9q0',
      },
    },
  },
];
