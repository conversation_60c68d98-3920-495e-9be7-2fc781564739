import { Prisma } from '@prisma/client';

export const qualifications: Prisma.QualificationCreateInput[] = [
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p',
    qualification: "Bachelor's Degree",
    specialization: 'Computer Science',
    passingYear: new Date('2020-05-15'),
    obtainedMarks: 3.2,
    totalMarks: 4,
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q',
    qualification: "Master's Degree",
    specialization: 'Data Science',
    passingYear: new Date('2022-08-20'),
    obtainedMarks: 3.6,
    totalMarks: 4,
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'c3d4e5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r',
    qualification: 'High School Diploma',
    specialization: 'Science',
    passingYear: new Date('2018-03-10'),
    obtainedMarks: 450,
    totalMarks: 500,
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-n4o5p6q7r8s',
    qualification: 'Diploma',
    specialization: 'Electrical Engineering',
    passingYear: new Date('2019-11-30'),
    obtainedMarks: 780,
    totalMarks: 800,
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-o5p6q7r8s9t',
    qualification: 'PhD',
    specialization: 'Physics',
    passingYear: new Date('2021-07-01'),
    obtainedMarks: 3.8,
    totalMarks: 4,
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
];
