import { Prisma } from '@prisma/client';

export const contacts: Prisma.ContactCreateInput[] = [
  // Harvard University Contacts
  {
    id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-555-1234',
    designation: 'Admissions Officer',
    department: 'Undergraduate Admissions',
    notes: 'Handles first-year student admissions.',
    university: { connect: { id: '11111111-1111-1111-1111-111111111111' } },
  },
  {
    id: 'bc9de7f6-1234-5678-90ab-cdef98765432',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-555-5678',
    designation: 'International Student Coordinator',
    department: 'International Office',
    notes: 'Manages visa documentation for international students.',
    university: { connect: { id: '11111111-1111-1111-1111-111111111111' } },
  },

  // Stanford University Contacts
  {
    id: 'ff43aa89-bc12-4d56-987e-12cd345f67a8',
    name: '<PERSON>',
    email: 'char<PERSON>.<EMAIL>',
    phone: '******-555-4321',
    designation: 'Graduate Admissions Director',
    department: 'Graduate Admissions',
    notes: 'Oversees graduate program applications and interviews.',
    university: { connect: { id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01' } },
  },
  {
    id: '78a9b2c3-d456-ef12-3456-7890abcdef12',
    name: 'Diana Prince',
    email: '<EMAIL>',
    phone: '******-555-8765',
    designation: 'Scholarship Coordinator',
    department: 'Financial Aid Office',
    notes: 'Manages scholarship distribution and financial aid queries.',
    university: { connect: { id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01' } },
  },

  // MIT Contacts
  {
    id: '12ef34ab-5678-9cde-f012-3456789abcde',
    name: 'Ethan Hunt',
    email: '<EMAIL>',
    phone: '******-555-6789',
    designation: 'Research Grant Coordinator',
    department: 'Sponsored Research Office',
    notes: 'Coordinates grant funding for research projects.',
    university: { connect: { id: '33333333-3333-3333-3333-333333333333' } },
  },
  {
    id: 'abcdef12-3456-7890-abcd-ef9876543210',
    name: 'Fiona Gallagher',
    email: '<EMAIL>',
    phone: '******-555-2345',
    designation: 'Exchange Program Manager',
    department: 'International Programs Office',
    notes: 'Oversees student exchange programs with partner universities.',
    university: { connect: { id: '33333333-3333-3333-3333-333333333333' } },
  },
];
