import { Prisma } from '@prisma/client';

export const offices: Prisma.OfficeCreateInput[] = [
  {
    id: 'g7h8i9j0-k1l2-3m4n-5o6p-7q8r9stuvwx',
    name: 'SF Office',
    city: {
      connect: { id: 'f6g7h8i9-j0k1-2l3m-4n5o-6p7q8r9stuv' },
    },
  },
  {
    id: 'h1i2j3k4-l5m6-7n8o-9p0q-r1s2t3u4v5w6',
    name: 'NY Office',
    city: {
      connect: { id: 'f6g7h8i9-j0k1-2l3m-4n5o-6p7q8r9stuv' },
    },
  },
];
