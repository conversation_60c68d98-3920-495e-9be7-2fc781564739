import { Prisma } from '@prisma/client';

export const applications: Prisma.ApplicationCreateInput[] = [
  {
    id: 'k1l2m3n4-o5p6-7q8r-9stu-vwxyz012345',
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
    university: {
      connect: {
        id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01',
      },
    },
    program: {
      connect: {
        id: 'f65c18a2-6c28-48de-b5c9-1e6349cfa67b',
      },
    },
    status: 'APPLIED',
    appliedDate: new Date(),
    decisionDate: new Date(),
    visaAppliedDate: new Date(),
    visaDecisionDate: new Date(),
    notes: 'XYZ',
    lastInstituteAttended: 'Some Institute',
    lastInstituteDegree: 'Bachelors',
    interestedDegreeLevel: 'MASTERS',
  },
];
