import { Prisma } from '@prisma/client';

export const employees: Prisma.EmployeeCreateInput[] = [
  {
    id: 'h8i9j0k1-l2m3-4n5o-6p7q-8r9stuvwxyz',
    firstName: 'Ali',
    lastName: 'Azeem',
    phone: '123456789',
    user: {
      connect: {
        id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
      },
    },
    office: {
      connect: {
        id: 'g7h8i9j0-k1l2-3m4n-5o6p-7q8r9stuvwx',
      },
    },
    Country: {
      connect: {
        id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6p7q8r9s',
      },
    },
    Region: {
      connect: {
        id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7q8r9st',
      },
    },
    City: {
      connect: {
        id: 'f6g7h8i9-j0k1-2l3m-4n5o-6p7q8r9stuv',
      },
    },
  },
];
