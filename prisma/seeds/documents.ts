import { Prisma } from '@prisma/client';

export const documents: Prisma.DocumentCreateInput[] = [
  {
    id: '11111111-1111-1111-1111-111111111111',
    type: 'Passport',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-01-10'),
    fileUrl: 'https://example.com/documents/passport_john.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '22222222-2222-2222-2222-222222222222',
    type: 'ID Card',
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-01-15'),
    fileUrl: 'https://example.com/documents/idcard_john.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '33333333-3333-3333-3333-333333333333',
    type: 'Transcript',
    createdAt: new Date('2023-02-05'),
    updatedAt: new Date('2023-02-05'),
    fileUrl: 'https://example.com/documents/transcript_john.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '44444444-4444-4444-4444-444444444444',
    type: 'Diploma',
    createdAt: new Date('2023-02-10'),
    updatedAt: new Date('2023-02-10'),
    fileUrl: 'https://example.com/documents/diploma_jane.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '55555555-5555-5555-5555-555555555555',
    type: 'Certificate',
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2023-03-01'),
    fileUrl: 'https://example.com/documents/certificate_jane.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '66666666-6666-6666-6666-666666666666',
    type: 'Recommendation Letter',
    createdAt: new Date('2023-03-05'),
    updatedAt: new Date('2023-03-05'),
    fileUrl: 'https://example.com/documents/recommendation_jane.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '77777777-7777-7777-7777-777777777777',
    type: 'Visa',
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date('2023-03-10'),
    fileUrl: 'https://example.com/documents/visa_john.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '88888888-8888-8888-8888-888888888888',
    type: 'Birth Certificate',
    createdAt: new Date('2023-03-15'),
    updatedAt: new Date('2023-03-15'),
    fileUrl: 'https://example.com/documents/birth_certificate_jane.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: '99999999-9999-9999-9999-999999999999',
    type: 'Degree Certificate',
    createdAt: new Date('2023-04-01'),
    updatedAt: new Date('2023-04-01'),
    fileUrl: 'https://example.com/documents/degree_certificate_john.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
  {
    id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
    type: 'Other Document',
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date('2023-04-05'),
    fileUrl: 'https://example.com/documents/other_document.pdf',
    student: {
      connect: { id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123' },
    },
  },
];
