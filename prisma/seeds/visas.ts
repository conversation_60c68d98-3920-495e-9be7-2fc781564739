import { Prisma } from '@prisma/client';

export const visas: Prisma.VisaCreateInput[] = [
  {
    id: 'e9a7b8d9-47fa-46c2-8b2a-2f723e4d7eaa',
    type: 'STUDY',
    outcome: 'APPROVED',
    country: 'USA',
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'a3f4b9c2-5b6c-4a2a-9d5a-7f8b6c5d3e2f',
    type: 'WORK',
    outcome: 'PENDING',
    country: 'Canada',
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
  {
    id: 'b4c3d2e1-f7a8-4c9d-8e0f-1a2b3c4d5e6f',
    type: 'TOURIST',
    outcome: 'REJECTED',
    country: 'UK',
    yearOfRefusal: new Date('1997-05-15'),
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'c5d6e7f8-1a2b-3c4d-5e6f-7a8b9c0d1e2f',
    type: 'STUDY',
    outcome: 'APPROVED',
    country: 'Germany',
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
  {
    id: 'd6e7f8a9-2b3c-4d5e-6f7a-8b9c0d1e2f3a',
    type: 'WORK',
    outcome: 'APPROVED',
    country: 'France',
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'f7a8b9c0-3c4d-5e6f-7a8b-9c0d1e2f3a4b',
    type: 'TOURIST',
    outcome: 'PENDING',
    country: 'Australia',
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
  {
    id: 'a8b9c0d1-4d5e-6f7a-8b9c-0d1e2f3a4b5c',
    type: 'STUDY',
    outcome: 'REJECTED',
    yearOfRefusal: new Date('1997-05-15'),
    country: 'Japan',
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'b9c0d1e2-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
    type: 'WORK',
    outcome: 'APPROVED',
    country: 'India',
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
  {
    id: 'c0d1e2f3-6f7a-8b9c-0d1e-2f3a4b5c6d7e',
    type: 'TOURIST',
    outcome: 'PENDING',
    country: 'Brazil',
    student: {
      connect: {
        id: 'j0k1l2m3-n4o5-6p7q-8r9s-tuvwxyz0123',
      },
    },
  },
  {
    id: 'd1e2f3a4-7a8b-9c0d-1e2f-3a4b5c6d7e8f',
    type: 'STUDY',
    outcome: 'APPROVED',
    country: 'Italy',
    student: {
      connect: {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
      },
    },
  },
];
