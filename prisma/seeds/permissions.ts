import { Prisma } from '@prisma/client';

export const permissions: Prisma.PermissionCreateInput[] = [
  {
    id: 'd0f7e9c8-4b2e-4f7d-8d3f-2b4e9f0cf8e',
    name: 'manage:students',
    description: 'Manage students',
    group: 'STUDENTS',
  },
  {
    id: 'd0f7e9c8-4b2e-4f7d-8d3f-254e9f0c7f81',
    name: 'create:students',
    description: 'Manage students',
    group: 'STUDENTS',
  },
  {
    id: 'd0f7e9c8-4b2e-4f7d-8d3f-244e9f0c7f82',
    name: 'read:students',
    description: 'Manage students',
    group: 'STUDENTS',
  },
  {
    id: 'd0f7e9c8-4b2e-4f7d-8d3f-254e9f0c7f83',
    name: 'update:students',
    description: 'Manage students',
    group: 'STUDENTS',
  },
  {
    id: 'd0f7e9c8-4b2e-4f7d-8d3f-264e9f0c7f84',
    name: 'delete:students',
    description: 'Manage students',
    group: 'STUDENTS',
  },
  {
    id: 'e9d0c7f8-4e9d-489d-b7e3-4d2c8f7d9f0b',
    name: '*',
    description: 'All permissions',
    group: 'ADMIN',
  },
  {
    id: 'f0e9d8c7-4b2e-4f7d-8d3f-2b4ewf0c7f8e',
    name: 'manage:applications',
    description: 'Manage applications',
    group: 'APPLICATIONS',
  },
  {
    id: 'f0e9d8c7-4b2e-4f7d-8d3f-2b4e9f0c7f3e',
    name: 'create:applications',
    description: 'Manage applications',
    group: 'APPLICATIONS',
  },
  {
    id: 'f0e9d8c7-4b2e-4f7d-8d3f-2b4e9f057f8e',
    name: 'read:applications',
    description: 'Manage applications',
    group: 'APPLICATIONS',
  },
  {
    id: 'f0e9d8c7-4b2e-4f7d-8d3f-2b4e9f6c7f8e',
    name: 'update:applications',
    description: 'Manage applications',
    group: 'APPLICATIONS',
  },
  {
    id: 'f0e9d8c7-4b2e-4f7d-8d3f-2b4e9a0c7f8e',
    name: 'delete:applications',
    description: 'Manage applications',
    group: 'APPLICATIONS',
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
    name: 'manage:universities',
    description: 'Manage universities',
    group: 'UNIVERSITIES',
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2g3n4o5p6',
    name: 'create:universities',
    description: 'Manage universities',
    group: 'UNIVERSITIES',
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2h3n4o5p6',
    name: 'read:universities',
    description: 'Manage universities',
    group: 'UNIVERSITIES',
  },
  {
    id: 'a1b2c3d4-e5f6-7g8h-si0j-k1l2m3n4o5p6',
    name: 'update:universities',
    description: 'Manage universities',
    group: 'UNIVERSITIES',
  },
  {
    id: 'a1b2c3d4-e5f6-cg8h-9i0j-k1l2m3n4o5p6',
    name: 'delete:universities',
    description: 'Manage universities',
    group: 'UNIVERSITIES',
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
    name: 'manage:offices',
    description: 'Manage offices',
    group: 'OFFICES',
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3neo5p6q7',
    name: 'create:offices',
    description: 'Manage offices',
    group: 'OFFICES',
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3nqo5p6q7',
    name: 'read:offices',
    description: 'Manage offices',
    group: 'OFFICES',
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4k5p6q7',
    name: 'update:offices',
    description: 'Manage offices',
    group: 'OFFICES',
  },
  {
    id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2mln4o5p6q7',
    name: 'delete:offices',
    description: 'Manage offices',
    group: 'OFFICES',
  },
  {
    id: 'c3d4e5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r3',
    name: 'manage:cities',
    description: 'Manage cities',
    group: 'CITIES',
  },
  {
    id: 'c3d4e5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r8',
    name: 'create:cities',
    description: 'Manage cities',
    group: 'CITIES',
  },
  {
    id: 'c3d4l5f6-g7h8-9i0j-1k2l-m3n4o5p6q7r8',
    name: 'read:cities',
    description: 'Manage cities',
    group: 'CITIES',
  },
  {
    id: 'c3d4e5f6-glh8-9i0j-1k2l-m3n4o5p6q7r8',
    name: 'update:cities',
    description: 'Manage cities',
    group: 'CITIES',
  },
  {
    id: 'c3d4e5f6-g7h8-9i0j-1k2l-l3n4o5p6q7r8',
    name: 'delete:cities',
    description: 'Manage cities',
    group: 'CITIES',
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6p7q8r9',
    name: 'manage:regions',
    description: 'Manage regions',
    group: 'REGIONS',
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6poq8r9',
    name: 'create:regions',
    description: 'Manage regions',
    group: 'REGIONS',
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6p3q8r9',
    name: 'read:regions',
    description: 'Manage regions',
    group: 'REGIONS',
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6p758r9',
    name: 'update:regions',
    description: 'Manage regions',
    group: 'REGIONS',
  },
  {
    id: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o1p7q8r9',
    name: 'delete:regions',
    description: 'Manage regions',
    group: 'REGIONS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7q8r9s0',
    name: 'manage:countries',
    description: 'Manage countries',
    group: 'COUNTRIES',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7qgr9s0',
    name: 'create:countries',
    description: 'Manage countries',
    group: 'COUNTRIES',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7qkr9s0',
    name: 'read:countries',
    description: 'Manage countries',
    group: 'COUNTRIES',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6k7q8r9s0',
    name: 'update:countries',
    description: 'Manage countries',
    group: 'COUNTRIES',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6c7q8r9s0',
    name: 'delete:countries',
    description: 'Manage countries',
    group: 'COUNTRIES',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7q8r9s2',
    name: 'manage:contacts',
    description: 'Manage contacts',
    group: 'CONTACTS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3f4n-5o6p7q8r9s2',
    name: 'create:contacts',
    description: 'Manage contacts',
    group: 'CONTACTS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3f4n-5o6p7q8rfs2',
    name: 'read:contacts',
    description: 'Manage contacts',
    group: 'CONTACTS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3f4n-5o6p7g8r9s2',
    name: 'update:contacts',
    description: 'Manage contacts',
    group: 'CONTACTS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3f4n-5o6p7qqr9s2',
    name: 'delete:contacts',
    description: 'Manage contacts',
    group: 'CONTACTS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7w8r9s1',
    name: 'manage:workflows',
    description: 'Manage workflows',
    group: 'WORKFLOWS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6pwq8r9s1',
    name: 'create:workflows',
    description: 'Manage workflows',
    group: 'WORKFLOWS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7a8r9s1',
    name: 'read:workflows',
    description: 'Manage workflows',
    group: 'WORKFLOWS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6p7qsr9s1',
    name: 'update:workflows',
    description: 'Manage workflows',
    group: 'WORKFLOWS',
  },
  {
    id: 'e5f6g7h8-i9j0-1k2l-3m4n-5o6pgq8r9s1',
    name: 'delete:workflows',
    description: 'Manage workflows',
    group: 'WORKFLOWS',
  },
];
