import { Prisma } from '@prisma/client';

export const roles: Prisma.RoleCreateInput[] = [
  {
    id: '9f4c8e7d-12a3-4d9e-8c7d-4f0a4e5e8d3f',
    name: 'ADMI<PERSON>',
    description: 'Administrator',
    permissions: {
      connect: [
        {
          id: 'd0f7e9c8-4b2e-4f7d-8d3f-2b4e9f0cf8e',
        },
        {
          id: 'e9d0c7f8-4e9d-489d-b7e3-4d2c8f7d9f0b',
        },
      ],
    },
  },
  {
    id: 'c7f8e9d0-2b4e-489d-b7e3-4d2c8f7d9f0b',
    name: 'COUNSELL<PERSON>',
    description: 'Counsellor',
    permissions: {
      connect: [
        {
          id: 'd0f7e9c8-4b2e-4f7d-8d3f-2b4e9f0cf8e',
        },
      ],
    },
  },
  {
    id: 'b5c6d7e8-f9g0-1h2i-3j4k-l5m6n7o8p9q0',
    name: 'STUDENT',
    description: 'Student',
    permissions: {
      connect: [
        {
          id: 'd0f7e9c8-4b2e-4f7d-8d3f-2b4e9f0cf8e',
        },
      ],
    },
  },
];
