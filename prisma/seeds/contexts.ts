import { Prisma } from '@prisma/client';

export const contexts: Prisma.ContextCreateInput[] = [
  {
    id: 'c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6',
    sessionId: 'session-abc',
    browserUserAgent: 'Chrome/120',
    geolocation: '37.7749,-122.4194',
  },
  {
    id: 'd2e3f4g5-h6i7-j8k9-l0m1-n2o3p4q5r6s7',
    sessionId: 'session-def',
    browserUserAgent: 'Firefox/90',
    geolocation: '40.7128,-74.0060',
  },
  {
    id: 'e3f4g5h6-i7j8-k9l0-m1n2-o3p4q5r6s7t8',
    sessionId: 'session-ghi',
    browserUserAgent: 'Safari/14',
    geolocation: '34.0522,-118.2437',
  },
  {
    id: 'f4g5h6i7-j8k9-l0m1-n2o3-p4q5r6s7t8u9',
    sessionId: 'session-jkl',
    browserUserAgent: 'Edge/91',
    geolocation: '51.5074,-0.1278',
  },
  {
    id: 'g5h6i7j8-k9l0-m1n2-o3p4-q5r6s7t8u9v0',
    sessionId: 'session-mno',
    browserUserAgent: 'Chrome/95',
    geolocation: '48.8566,2.3522',
  },
];
