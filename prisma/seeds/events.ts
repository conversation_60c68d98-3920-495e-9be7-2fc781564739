import { Prisma } from '@prisma/client';

export const events: Prisma.EventCreateInput[] = [
  {
    id: 'ev1f2g3h4-i5j6-k7l8-m9n0-o1p2q3r4s5t6',
    timestamp: new Date('2024-03-11T10:00:00.000Z'),
    eventType: 'CREATE_STUDENT',
    type: 'STUDENT',
    entityId: 'student_123',
    fieldModified: ['firstName', 'lastName', 'email'],
    sessionId: 'session-abc',
    browserUserAgent: 'Chrome/120',
    geolocation: '37.7749,-122.4194',
    beforeSnapshot: {},
    afterSnapshot: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    actor: {
      create: {
        id: 'actor-create-student-1',
        userId: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
        role: 'COUNSELLOR',
        ipAddress: '127.0.0.1',
        deviceId: 'test-device'
      }
    },
    targetEntity: {
      create: {
        id: 'target-create-student-1',
        type: 'STUDENT',
        entityId: 'student_123',
        fieldModified: ['firstName', 'lastName', 'email']
      }
    },
    context: {
      create: {
        id: 'context-create-student-1',
        sessionId: 'session-abc',
        browserUserAgent: 'Chrome/120',
        geolocation: '37.7749,-122.4194'
      }
    }
  },
  {
    id: 'ev2f3g4h5-i6j7-k8l9-m0n1-o2p3q4r5s6t7',
    timestamp: new Date('2024-03-11T11:00:00.000Z'),
    eventType: 'USER_LOGIN',
    type: 'USER',
    entityId: 'user_456',
    fieldModified: ['lastLogin'],
    sessionId: 'session-def',
    browserUserAgent: 'Firefox/90',
    geolocation: '40.7128,-74.0060',
    beforeSnapshot: { lastLogin: null },
    afterSnapshot: { lastLogin: '2024-03-11T11:00:00.000Z' },
    actor: {
      create: {
        id: 'actor-user-login-1',
        userId: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
        role: 'ADMIN',
        ipAddress: '127.0.0.1',
        deviceId: 'test-device'
      }
    },
    targetEntity: {
      create: {
        id: 'target-user-login-1',
        type: 'USER',
        entityId: 'user_456',
        fieldModified: ['lastLogin']
      }
    },
    context: {
      create: {
        id: 'context-user-login-1',
        sessionId: 'session-def',
        browserUserAgent: 'Firefox/90',
        geolocation: '40.7128,-74.0060'
      }
    }
  },
  {
    id: 'ev3f4g5h6-i7j8-k9l0-m1n2-o3p4q5r6s7t8',
    timestamp: new Date('2024-03-11T12:00:00.000Z'),
    eventType: 'APPLICATION_STATUS_CHANGE',
    type: 'APPLICATION',
    entityId: 'application_789',
    fieldModified: ['status'],
    sessionId: 'session-ghi',
    browserUserAgent: 'Safari/14',
    geolocation: '34.0522,-118.2437',
    beforeSnapshot: { status: 'APPLIED' },
    afterSnapshot: { status: 'ACCEPTED' },
    actor: {
      create: {
        id: 'actor-status-change-1',
        userId: 'c3d4e5f6-g7h8-9i0j-k1l2-m3n4o5p6q7r8',
        role: 'COUNSELLOR',
        ipAddress: '127.0.0.1',
        deviceId: 'test-device'
      }
    },
    targetEntity: {
      create: {
        id: 'target-status-change-1',
        type: 'APPLICATION',
        entityId: 'application_789',
        fieldModified: ['status']
      }
    },
    context: {
      create: {
        id: 'context-status-change-1',
        sessionId: 'session-ghi',
        browserUserAgent: 'Safari/14',
        geolocation: '34.0522,-118.2437'
      }
    }
  },
  {
    id: 'ev4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9',
    timestamp: new Date('2024-03-11T13:00:00.000Z'),
    eventType: 'FILE_UPLOAD',
    type: 'DOCUMENT',
    entityId: 'document_101',
    fieldModified: ['fileUrl', 'type'],
    sessionId: 'session-jkl',
    browserUserAgent: 'Edge/91',
    geolocation: '51.5074,-0.1278',
    beforeSnapshot: {},
    afterSnapshot: {
      fileUrl: 'https://example.com/documents/passport.pdf',
      type: 'PASSPORT'
    },
    actor: {
      create: {
        id: 'actor-file-upload-1',
        userId: 'd4e5f6g7-h8i9-0j1k-l2m3-n4o5p6q7r8s9',
        role: 'STUDENT',
        ipAddress: '127.0.0.1',
        deviceId: 'test-device'
      }
    },
    targetEntity: {
      create: {
        id: 'target-file-upload-1',
        type: 'DOCUMENT',
        entityId: 'document_101',
        fieldModified: ['fileUrl', 'type']
      }
    },
    context: {
      create: {
        id: 'context-file-upload-1',
        sessionId: 'session-jkl',
        browserUserAgent: 'Edge/91',
        geolocation: '51.5074,-0.1278'
      }
    }
  },
  {
    id: 'ev5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0',
    timestamp: new Date('2024-03-11T14:00:00.000Z'),
    eventType: 'NOTE_ADDED',
    type: 'APPLICATION',
    entityId: 'application_202',
    fieldModified: ['notes'],
    sessionId: 'session-mno',
    browserUserAgent: 'Chrome/95',
    geolocation: '48.8566,2.3522',
    beforeSnapshot: { notes: null },
    afterSnapshot: { notes: 'Student requested additional information about the program' },
    actor: {
      create: {
        id: 'actor-note-added-1',
        userId: 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0',
        role: 'COUNSELLOR',
        ipAddress: '127.0.0.1',
        deviceId: 'test-device'
      }
    },
    targetEntity: {
      create: {
        id: 'target-note-added-1',
        type: 'APPLICATION',
        entityId: 'application_202',
        fieldModified: ['notes']
      }
    },
    context: {
      create: {
        id: 'context-note-added-1',
        sessionId: 'session-mno',
        browserUserAgent: 'Chrome/95',
        geolocation: '48.8566,2.3522'
      }
    }
  }
];
