import { Prisma } from '@prisma/client';

export const programs: Prisma.ProgramCreateInput[] = [
  // Programs for Harvard University (id: '11111111-1111-1111-1111-111111111111')
  {
    id: 'c99fe0bd-4167-4b11-9fdd-7a0fbd5e2f25',
    name: 'Computer Science',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 60000.0,
    applicationDeadline: new Date('2024-12-01T00:00:00Z'),
    university: {
      connect: {
        id: '11111111-1111-1111-1111-111111111111',
      },
    },
  },
  {
    id: '8a3c8e31-5d2b-4f65-b4d5-96d3e09d8f4c',
    name: 'Business Administration',
    duration: '2 years',
    degreeType: 'MBA',
    tuitionFees: 75000.0,
    applicationDeadline: new Date('2024-11-15T00:00:00Z'),
    university: {
      connect: {
        id: '11111111-1111-1111-1111-111111111111',
      },
    },
  },
  {
    id: 'b152e27c-3f2b-44c9-a9ea-468a3f7b65c0',
    name: 'Law',
    duration: '3 years',
    degreeType: 'JD',
    tuitionFees: 80000.0,
    applicationDeadline: new Date('2024-10-20T00:00:00Z'),
    university: {
      connect: {
        id: '11111111-1111-1111-1111-111111111111',
      },
    },
  },

  // Programs for Stanford University (id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01')
  {
    id: 'f65c18a2-6c28-48de-b5c9-1e6349cfa67b',
    name: 'Engineering',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 65000.0,
    applicationDeadline: new Date('2024-12-05T00:00:00Z'),
    university: {
      connect: {
        id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01',
      },
    },
  },
  {
    id: 'e0b5f6d1-5d71-4b2f-9f3a-c9bfca7a2b1f',
    name: 'Medicine',
    duration: '6 years',
    degreeType: 'MD',
    tuitionFees: 120000.0,
    applicationDeadline: new Date('2024-09-30T00:00:00Z'),
    university: {
      connect: {
        id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01',
      },
    },
  },
  {
    id: '2f3f4470-1a6b-4f90-a6a2-2a4c7b6f1d23',
    name: 'Design',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 55000.0,
    applicationDeadline: new Date('2024-11-01T00:00:00Z'),
    university: {
      connect: {
        id: 'i9j0k1l2-m3n4-5o6p-7q8r-9stuvwxyz01',
      },
    },
  },

  // Programs for MIT (id: '33333333-3333-3333-3333-333333333333')
  {
    id: 'a33d1fc7-3c2b-44c4-8b47-d2b4a6c0c8e4',
    name: 'Electrical Engineering',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 70000.0,
    applicationDeadline: new Date('2024-12-10T00:00:00Z'),
    university: {
      connect: {
        id: '33333333-3333-3333-3333-333333333333',
      },
    },
  },
  {
    id: 'd5a4b2e1-2e6f-4a97-b0f3-f6cba8c0a5d2',
    name: 'Physics',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 68000.0,
    applicationDeadline: new Date('2024-11-20T00:00:00Z'),
    university: {
      connect: {
        id: '33333333-3333-3333-3333-333333333333',
      },
    },
  },
  {
    id: 'f7c98d8b-91c4-4f45-8b22-1a3e2d6e2f5b',
    name: 'Mathematics',
    duration: '4 years',
    degreeType: 'Bachelor',
    tuitionFees: 65000.0,
    applicationDeadline: new Date('2024-12-15T00:00:00Z'),
    university: {
      connect: {
        id: '33333333-3333-3333-3333-333333333333',
      },
    },
  },
];
