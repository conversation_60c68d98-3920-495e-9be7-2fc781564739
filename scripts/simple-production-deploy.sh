#!/bin/bash

# Simple Production Deployment - No Custom Networks
# This avoids all network conflicts by using Docker's default bridge

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
VERSION="$1"
if [[ -z "$VERSION" ]]; then
    error "Usage: $0 <version>"
    exit 1
fi

APP_NAME="fes-crm-backend"

log "Starting simple production deployment for $APP_NAME version $VERSION"

# Health check function
health_check() {
    local retries=15
    local interval=10
    
    info "Running health checks..."
    
    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:5000/" > /dev/null 2>&1; then
            log "Health check passed (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed (attempt $i/$retries)"
        sleep $interval
    done
    
    error "Health check failed after $retries attempts"
    return 1
}

# Main deployment
main() {
    # Clean up everything first
    info "Cleaning up existing containers and networks..."
    docker stop $(docker ps -aq) 2>/dev/null || true
    docker rm $(docker ps -aq) 2>/dev/null || true
    docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" | xargs -r docker network rm 2>/dev/null || true
    docker system prune -f
    
    # Update image version
    info "Updating docker-compose configuration..."
    sed -i "s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g" docker-compose.prod.yml
    
    # Create simple override file (no custom networks)
    info "Creating network override for bridge mode..."
    cat > docker-compose.prod.override.yml << 'EOF'
services:
  backend:
    network_mode: "bridge"
    ports:
      - "5000:5000"
  watchtower:
    network_mode: "bridge"
EOF
    
    # Start deployment
    info "Starting production deployment..."
    docker compose -f docker-compose.prod.yml -f docker-compose.prod.override.yml up -d
    
    # Wait for startup
    info "Waiting for application to start..."
    sleep 30
    
    # Health check
    if health_check; then
        log "✅ Deployment successful!"
        
        # Show status
        info "Container status:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        # Test endpoint
        info "Testing application endpoint..."
        curl -v http://localhost:5000/ || warn "Endpoint test failed but containers are running"
        
        log "🎉 Simple production deployment completed successfully!"
        
    else
        error "❌ Deployment failed"
        
        info "Container logs:"
        docker compose -f docker-compose.prod.yml -f docker-compose.prod.override.yml logs backend
        
        exit 1
    fi
}

# Run main function
main "$@"
