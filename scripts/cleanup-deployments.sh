#!/bin/bash

# Deployment cleanup script for FES CRM Backend
# This script helps manage deployment folders and backups

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
DEPLOYMENT_DIR="/root"
MAIN_APP="fes-crm-backend"
KEEP_BACKUPS=3  # Number of backups to keep

# Function to show current status
show_status() {
    log "Current deployment status:"
    echo
    
    cd "$DEPLOYMENT_DIR"
    
    # Main deployment
    if [ -d "$MAIN_APP" ]; then
        echo "📁 Main Deployment: $MAIN_APP"
        if [ -f "$MAIN_APP/.version" ]; then
            VERSION=$(cat "$MAIN_APP/.version")
            echo "   Version: $VERSION"
        fi
        if [ -f "$MAIN_APP/.deployment-time" ]; then
            DEPLOY_TIME=$(cat "$MAIN_APP/.deployment-time")
            echo "   Deployed: $DEPLOY_TIME"
        fi
        
        # Check if it's running
        if docker compose -f "$MAIN_APP/docker-compose.prod.yml" ps | grep -q "Up"; then
            echo "   Status: ✅ Running"
        else
            echo "   Status: ❌ Not running"
        fi
    else
        error "Main deployment directory not found: $MAIN_APP"
    fi
    
    echo
    
    # Backup directories
    BACKUPS=$(find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-backup-*" -type d | sort)
    if [ -n "$BACKUPS" ]; then
        echo "📦 Backup Directories:"
        echo "$BACKUPS" | while read backup; do
            BACKUP_NAME=$(basename "$backup")
            BACKUP_DATE=$(echo "$BACKUP_NAME" | sed "s/${MAIN_APP}-backup-//")
            echo "   $BACKUP_NAME (Date: $BACKUP_DATE)"
        done
    else
        echo "📦 No backup directories found"
    fi
    
    echo
    
    # Temporary directories
    TEMP_DIRS=$(find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-new*" -type d)
    if [ -n "$TEMP_DIRS" ]; then
        echo "🗂️  Temporary Directories:"
        echo "$TEMP_DIRS" | while read temp; do
            TEMP_NAME=$(basename "$temp")
            echo "   $TEMP_NAME (Should be cleaned up)"
        done
    else
        echo "🗂️  No temporary directories found"
    fi
    
    echo
}

# Function to clean up temporary directories
cleanup_temp() {
    log "Cleaning up temporary directories..."
    
    cd "$DEPLOYMENT_DIR"
    
    # Remove temporary deployment directories
    TEMP_DIRS=$(find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-new*" -type d)
    if [ -n "$TEMP_DIRS" ]; then
        echo "$TEMP_DIRS" | while read temp; do
            TEMP_NAME=$(basename "$temp")
            warn "Removing temporary directory: $TEMP_NAME"
            rm -rf "$temp"
        done
        log "Temporary directories cleaned up"
    else
        info "No temporary directories to clean up"
    fi
}

# Function to manage backups (keep only the latest N)
manage_backups() {
    log "Managing backup directories (keeping latest $KEEP_BACKUPS)..."
    
    cd "$DEPLOYMENT_DIR"
    
    # Get all backup directories sorted by date (oldest first)
    BACKUPS=$(find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-backup-*" -type d | sort)
    BACKUP_COUNT=$(echo "$BACKUPS" | wc -l)
    
    if [ -z "$BACKUPS" ] || [ "$BACKUP_COUNT" -eq 0 ]; then
        info "No backup directories found"
        return
    fi
    
    info "Found $BACKUP_COUNT backup directories"
    
    if [ "$BACKUP_COUNT" -gt "$KEEP_BACKUPS" ]; then
        REMOVE_COUNT=$((BACKUP_COUNT - KEEP_BACKUPS))
        warn "Removing $REMOVE_COUNT old backup(s) (keeping latest $KEEP_BACKUPS)"
        
        echo "$BACKUPS" | head -n "$REMOVE_COUNT" | while read backup; do
            BACKUP_NAME=$(basename "$backup")
            warn "Removing old backup: $BACKUP_NAME"
            rm -rf "$backup"
        done
        
        log "Old backups cleaned up"
    else
        info "Backup count ($BACKUP_COUNT) is within limit ($KEEP_BACKUPS)"
    fi
}

# Function to show disk usage
show_disk_usage() {
    log "Disk usage analysis:"
    echo
    
    cd "$DEPLOYMENT_DIR"
    
    # Main deployment
    if [ -d "$MAIN_APP" ]; then
        SIZE=$(du -sh "$MAIN_APP" | cut -f1)
        echo "📁 $MAIN_APP: $SIZE"
    fi
    
    # Backups
    find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-backup-*" -type d | while read backup; do
        BACKUP_NAME=$(basename "$backup")
        SIZE=$(du -sh "$backup" | cut -f1)
        echo "📦 $BACKUP_NAME: $SIZE"
    done
    
    # Temporary directories
    find "$DEPLOYMENT_DIR" -maxdepth 1 -name "${MAIN_APP}-new*" -type d | while read temp; do
        TEMP_NAME=$(basename "$temp")
        SIZE=$(du -sh "$temp" | cut -f1)
        echo "🗂️  $TEMP_NAME: $SIZE"
    done
    
    echo
    echo "Total deployment directory usage:"
    du -sh "$DEPLOYMENT_DIR"
}

# Main menu
show_menu() {
    echo
    info "Deployment Cleanup Options:"
    echo "1. Show current status"
    echo "2. Clean up temporary directories"
    echo "3. Manage backups (keep latest $KEEP_BACKUPS)"
    echo "4. Show disk usage"
    echo "5. Full cleanup (temp + old backups)"
    echo "6. Exit"
    echo
}

# Main execution
main() {
    log "FES CRM Backend Deployment Cleanup Tool"
    
    if [ "$1" = "--auto" ]; then
        log "Running automatic cleanup..."
        cleanup_temp
        manage_backups
        show_status
        log "Automatic cleanup completed"
        exit 0
    fi
    
    while true; do
        show_menu
        read -p "Choose an option (1-6): " choice
        
        case $choice in
            1)
                show_status
                ;;
            2)
                cleanup_temp
                ;;
            3)
                manage_backups
                ;;
            4)
                show_disk_usage
                ;;
            5)
                cleanup_temp
                manage_backups
                show_status
                ;;
            6)
                log "Exiting cleanup tool"
                exit 0
                ;;
            *)
                error "Invalid option. Please choose 1-6."
                ;;
        esac
        
        echo
        read -p "Press Enter to continue..."
    done
}

# Run main function
main "$@"
