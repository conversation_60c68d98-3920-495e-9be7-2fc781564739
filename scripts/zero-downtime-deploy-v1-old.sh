#!/bin/bash

# Zero-Downtime Deployment Script
# This script implements blue-green deployment strategy with /root path

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
DEPLOYMENT_TIMEOUT=600
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Help function
show_help() {
    cat << EOF
Zero-Downtime Deployment Script for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Docker image version/tag to deploy
    -e, --env-file FILE      Environment file path (default: .env)
    -t, --timeout SECONDS   Deployment timeout (default: 600)
    -r, --retries COUNT     Health check retries (default: 30)
    -i, --interval SECONDS  Health check interval (default: 10)
    -f, --force             Force deployment without confirmation
    -d, --dry-run           Show what would be done without executing
    -h, --help              Show this help message

EXAMPLES:
    $0 -v latest                    # Deploy latest version
    $0 -v abc123 -f                 # Force deploy specific commit
    $0 -v latest -d                 # Dry run deployment
    $0 -v latest -t 900 -r 50       # Custom timeout and retries

NOTE: This script uses /root as the base directory for deployments.

EOF
}

# Parse command line arguments
VERSION=""
ENV_FILE=".env"
FORCE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -t|--timeout)
            DEPLOYMENT_TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            HEALTH_CHECK_RETRIES="$2"
            shift 2
            ;;
        -i|--interval)
            HEALTH_CHECK_INTERVAL="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$VERSION" ]]; then
    error "Version is required. Use -v or --version to specify."
    show_help
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]]; then
    error "Environment file not found: $ENV_FILE"
    exit 1
fi

# Load environment variables
set -a
source "$ENV_FILE"
set +a

# Dry run mode
if [[ "$DRY_RUN" == "true" ]]; then
    info "DRY RUN MODE - No actual changes will be made"
    info "Would deploy version: $VERSION"
    info "Using environment file: $ENV_FILE"
    info "Deployment timeout: ${DEPLOYMENT_TIMEOUT}s"
    info "Health check retries: $HEALTH_CHECK_RETRIES"
    info "Health check interval: ${HEALTH_CHECK_INTERVAL}s"
    info "Base directory: /root"
    exit 0
fi

# Confirmation prompt
if [[ "$FORCE" != "true" ]]; then
    echo
    info "Deployment Configuration:"
    echo "  Version: $VERSION"
    echo "  Environment: $ENV_FILE"
    echo "  Timeout: ${DEPLOYMENT_TIMEOUT}s"
    echo "  Health checks: $HEALTH_CHECK_RETRIES retries every ${HEALTH_CHECK_INTERVAL}s"
    echo "  Base directory: /root"
    echo
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        warn "Deployment cancelled by user"
        exit 0
    fi
fi

# Health check function
health_check() {
    local port=$1
    local retries=$2
    local interval=$3
    local service_name=${4:-"service"}

    info "Running health checks for $service_name on port $port..."

    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
            log "Health check passed for $service_name on port $port (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed for $service_name on port $port (attempt $i/$retries)"
        sleep $interval
    done

    error "Health check failed after $retries attempts for $service_name on port $port"
    return 1
}

# Smoke tests function
run_smoke_tests() {
    local port=$1
    local service_name=${2:-"service"}

    info "Running smoke tests for $service_name on port $port..."

    # Test 1: API Health
    if ! curl -f -s "http://localhost:${port}/" | grep -q "Hello World"; then
        error "Smoke test failed: API not responding correctly for $service_name"
        return 1
    fi

    # Test 2: API Response Time
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:${port}/")
    if (( $(echo "$response_time > 5.0" | bc -l) )); then
        warn "Slow response time detected: ${response_time}s for $service_name"
    fi

    log "All smoke tests passed for $service_name"
    return 0
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        error "Deployment failed with exit code $exit_code"
        warn "Check logs and consider manual rollback if necessary"
    fi
    exit $exit_code
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment function
main() {
    log "Starting zero-downtime deployment for $APP_NAME version $VERSION"

    # Pre-deployment checks
    info "Running pre-deployment checks..."

    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running or not accessible"
        exit 1
    fi

    # Check if required image exists
    if ! docker image inspect "${APP_NAME}:${VERSION}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${VERSION} not found"
        exit 1
    fi

    # Get current deployment info
    CURRENT_VERSION=""
    if [[ -f ".version" ]]; then
        CURRENT_VERSION=$(cat .version)
        info "Current version: $CURRENT_VERSION"
    else
        info "No previous deployment found"
    fi

    # Create backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_DIR="/root/${APP_NAME}-backup-${BACKUP_TIMESTAMP}"

    # Backup current deployment (including .env)
    if [[ -n "$CURRENT_VERSION" ]]; then
        info "Creating backup of current deployment..."
        cp -r . "$BACKUP_DIR"
        log "Backup created: $BACKUP_DIR"

        # Ensure .env is preserved
        if [[ -f ".env" ]]; then
            cp ".env" "/tmp/.env.deployment.backup"
            log "Environment file backed up to /tmp/.env.deployment.backup"
        else
            error "No .env file found - deployment cannot proceed"
            error "Please ensure .env file exists before deployment"
            exit 1
        fi
    fi

    # Database migration safety check
    info "Checking database migration safety..."
    if ! docker run --rm --env-file "$ENV_FILE" --network host \
        "${APP_NAME}:${VERSION}" \
        sh -c "npx prisma migrate deploy --schema=./prisma/schema.prisma" > /dev/null 2>&1; then
        error "Database migration failed or unsafe"
        exit 1
    fi
    log "Database migrations completed successfully"

    # Blue-Green Deployment Strategy
    info "Starting blue-green deployment..."

    # Step 1: Start green environment on different port
    GREEN_PORT=5001
    GREEN_COMPOSE_FILE="docker-compose.green.yml"

    # Create green environment compose file
    sed "s|5000:5000|${GREEN_PORT}:5000|g; s|fes_crm_prod|fes_crm_green|g; s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g" \
        docker-compose.prod.yml > "$GREEN_COMPOSE_FILE"

    info "Starting green environment on port $GREEN_PORT..."
    docker compose -f "$GREEN_COMPOSE_FILE" up -d

    # Wait for green environment to be ready
    info "Waiting for green environment to initialize..."
    sleep 30

    # Health check green environment
    if ! health_check $GREEN_PORT $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL "green environment"; then
        error "Green environment health check failed"
        docker compose -f "$GREEN_COMPOSE_FILE" down --remove-orphans
        rm -f "$GREEN_COMPOSE_FILE"
        exit 1
    fi

    # Run smoke tests on green environment
    if ! run_smoke_tests $GREEN_PORT "green environment"; then
        error "Green environment smoke tests failed"
        docker compose -f "$GREEN_COMPOSE_FILE" down --remove-orphans
        rm -f "$GREEN_COMPOSE_FILE"
        exit 1
    fi

    # Step 2: Traffic switch - Stop blue, start green on main port
    info "Switching traffic from blue to green environment..."

    # Stop blue environment
    if docker compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        info "Stopping blue environment..."
        docker compose -f docker-compose.prod.yml down --remove-orphans
    fi

    # Wait for network cleanup
    info "Waiting for network cleanup..."
    sleep 5

    # Update main compose file with new version
    sed -i "s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g" docker-compose.prod.yml

    # Start new version on main port
    info "Starting new version on main port..."
    docker compose -f docker-compose.prod.yml up -d

    # Wait for main environment to stabilize before cleanup
    info "Waiting for main environment to stabilize..."
    sleep 10

    # Cleanup green environment
    info "Cleaning up green environment..."
    docker compose -f "$GREEN_COMPOSE_FILE" down --remove-orphans
    rm -f "$GREEN_COMPOSE_FILE"

    # Final health check on main port
    info "Running final health checks on main port..."
    if ! health_check 5000 $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL "production environment"; then
        error "Final health check failed on main port"

        # Emergency rollback
        if [[ -d "$BACKUP_DIR" ]]; then
            warn "Performing emergency rollback..."
            docker compose -f docker-compose.prod.yml down --remove-orphans
            cp -r "$BACKUP_DIR"/* .
            docker compose -f docker-compose.prod.yml up -d

            if health_check 5000 10 5 "rollback environment"; then
                log "Emergency rollback successful"
            else
                error "Emergency rollback failed - manual intervention required"
            fi
        fi
        exit 1
    fi

    # Final smoke tests
    if ! run_smoke_tests 5000 "production environment"; then
        warn "Production smoke tests failed, but deployment will continue"
    fi

    # Save deployment info
    echo "$VERSION" > .version
    echo "$(date)" > .deployment-time
    echo "$BACKUP_TIMESTAMP" > .last-backup

    # Cleanup old backups (keep last 5)
    find /root -name "${APP_NAME}-backup-*" -type d | sort | head -n -5 | xargs rm -rf 2>/dev/null || true

    # Cleanup Docker resources
    info "Cleaning up Docker resources..."
    docker system prune -f > /dev/null 2>&1 || true
    docker image prune -f > /dev/null 2>&1 || true

    # Display final status
    log "Zero-downtime deployment completed successfully!"
    log "New version $VERSION is now live"

    info "Deployment Summary:"
    echo "  Previous version: ${CURRENT_VERSION:-none}"
    echo "  New version: $VERSION"
    echo "  Backup location: $BACKUP_DIR"
    echo "  Deployment time: $(date)"

    # Show running containers
    info "Running containers:"
    docker compose -f docker-compose.prod.yml ps
}

# Run main function
main "$@"
