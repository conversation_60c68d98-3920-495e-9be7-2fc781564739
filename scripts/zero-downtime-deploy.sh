#!/bin/bash

# Zero-Downtime Deployment Script v2 for FES CRM Backend
# This version uses a more robust approach to avoid network conflicts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_NAME="fes-crm-backend"
BLUE_PORT=5000
GREEN_PORT=5001
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10
DEPLOYMENT_TIMEOUT=600

# Show help
show_help() {
    cat << EOF
Zero-Downtime Deployment Script v2 for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Docker image version/tag to deploy
    -f, --force             Force deployment without confirmation
    -h, --help              Show this help message

EXAMPLES:
    $0 -v latest                    # Deploy latest version
    $0 -v abc123 -f                 # Force deploy specific commit

EOF
}

# Parse command line arguments
VERSION=""
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$VERSION" ]]; then
    error "Version is required. Use -v or --version to specify."
    show_help
    exit 1
fi

# Health check function
health_check() {
    local port=$1
    local retries=${2:-10}
    local interval=${3:-5}
    local service_name=${4:-"service"}

    info "Running health checks for $service_name on port $port..."

    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
            log "Health check passed for $service_name on port $port (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed for $service_name on port $port (attempt $i/$retries)"
        sleep $interval
    done

    error "Health check failed after $retries attempts for $service_name on port $port"
    return 1
}

# Smoke tests function
run_smoke_tests() {
    local port=$1
    local service_name=${2:-"service"}

    info "Running smoke tests for $service_name on port $port..."

    # Test 1: API Health
    if ! curl -f -s "http://localhost:${port}/" | grep -q "Hello World"; then
        error "Smoke test failed: API not responding correctly for $service_name"
        return 1
    fi

    log "All smoke tests passed for $service_name"
    return 0
}

# Network cleanup function
cleanup_networks() {
    info "Cleaning up conflicting Docker networks..."

    # Remove specific conflicting networks
    local conflicting_networks=(
        "clones-backend_clone_backend"
        "fes-crm-backend-new_fes_crm_network"
        "fes-crm-backend_fes_crm_network"
    )

    for network in "${conflicting_networks[@]}"; do
        if docker network ls --format "{{.Name}}" | grep -q "^${network}$"; then
            warn "Removing conflicting network: $network"
            docker network rm "$network" 2>/dev/null || true
        fi
    done

    # Also remove any networks using the old subnet
    info "Checking for networks using conflicting subnets..."
    local networks_to_check=$(docker network ls --format "{{.Name}}" | grep -E "(fes|crm|clone)")

    for network in $networks_to_check; do
        if docker network inspect "$network" 2>/dev/null | grep -q "172.20.0.0/16"; then
            warn "Removing network with conflicting subnet: $network"
            docker network rm "$network" 2>/dev/null || true
        fi
    done

    log "Network cleanup completed"
}

# Create green environment using existing network
create_green_environment() {
    local green_image="$1"

    info "Creating green environment on port $GREEN_PORT..."

    # Stop any existing green container
    docker stop fes_crm_green 2>/dev/null || true
    docker rm fes_crm_green 2>/dev/null || true

    # Get the network name from the current production setup
    local network_name="fes-crm-backend_fes_crm_network"

    # Create network if it doesn't exist (with fixed subnet)
    if ! docker network ls --format "{{.Name}}" | grep -q "^${network_name}$"; then
        info "Creating production network..."
        docker network create \
            --driver bridge \
            --subnet **********/16 \
            --label "com.docker.compose.network=fes_crm" \
            "$network_name"
    fi

    # Start green container using the same network as production
    info "Starting green container..."
    docker run -d \
        --name fes_crm_green \
        --network "$network_name" \
        -p "${GREEN_PORT}:5000" \
        --env-file .env \
        --restart unless-stopped \
        --health-cmd "curl -f http://localhost:5000/ || exit 1" \
        --health-interval 15s \
        --health-timeout 5s \
        --health-retries 3 \
        --health-start-period 30s \
        "$green_image"

    log "Green environment created successfully"
}

# Traffic switch function
switch_traffic() {
    local new_image="$1"

    info "Switching traffic from blue to green..."

    # Update docker-compose.prod.yml with new image
    sed -i "s|${APP_NAME}:.*|${new_image}|g" docker-compose.prod.yml

    # Stop blue environment (current production)
    info "Stopping blue environment..."
    docker compose -f docker-compose.prod.yml down --remove-orphans

    # Wait for network stabilization
    sleep 5

    # Start new production environment
    info "Starting new production environment..."
    docker compose -f docker-compose.prod.yml up -d

    # Wait for startup
    sleep 15

    # Clean up green container
    info "Cleaning up green environment..."
    docker stop fes_crm_green 2>/dev/null || true
    docker rm fes_crm_green 2>/dev/null || true

    log "Traffic switch completed"
}

# Main deployment function
main() {
    log "Starting zero-downtime deployment for $APP_NAME version $VERSION"

    # Check if we're in the right directory
    if [[ ! -f "docker-compose.prod.yml" ]]; then
        error "docker-compose.prod.yml not found. Please run this script from the project root."
        exit 1
    fi

    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running or not accessible"
        exit 1
    fi

    # Check if required image exists
    if ! docker image inspect "${APP_NAME}:${VERSION}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${VERSION} not found"
        exit 1
    fi

    # Confirmation prompt
    if [[ "$FORCE" != "true" ]]; then
        echo
        info "Zero-Downtime Deployment Configuration:"
        echo "  Version: $VERSION"
        echo "  Blue Port (Production): $BLUE_PORT"
        echo "  Green Port (Testing): $GREEN_PORT"
        echo "  Health checks: $HEALTH_CHECK_RETRIES retries every ${HEALTH_CHECK_INTERVAL}s"
        echo
        read -p "Continue with zero-downtime deployment? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            warn "Deployment cancelled by user"
            exit 0
        fi
    fi

    # Get current deployment info
    CURRENT_VERSION=""
    if [[ -f ".version" ]]; then
        CURRENT_VERSION=$(cat .version)
        info "Current version: $CURRENT_VERSION"
    else
        info "No previous deployment found"
    fi

    # Create backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_DIR="/root/${APP_NAME}-backup-${BACKUP_TIMESTAMP}"

    # Backup current deployment
    if [[ -n "$CURRENT_VERSION" ]]; then
        info "Creating backup of current deployment..."
        cp -r . "$BACKUP_DIR"
        log "Backup created: $BACKUP_DIR"
    fi

    # Clean up conflicting networks
    cleanup_networks

    # Fix network configuration in docker-compose.prod.yml
    info "Updating network configuration in docker-compose.prod.yml..."

    # Ensure we have the correct network configuration
    if grep -q "172\.20\.0\.0/16" docker-compose.prod.yml; then
        warn "Found old network configuration, updating..."
        sed -i 's/172\.20\.0\.0\/16/**********\/16/g' docker-compose.prod.yml
        log "Network configuration updated to use **********/16"
    else
        log "Network configuration is already correct"
    fi

    # Verify the network configuration
    if grep -q "172\.27\.0\.0/16" docker-compose.prod.yml; then
        log "✅ Network configuration verified: **********/16"
    else
        error "❌ Network configuration is incorrect"
        grep -A 5 "subnet:" docker-compose.prod.yml || echo "No subnet configuration found"
        exit 1
    fi

    # Step 1: Create and test green environment
    create_green_environment "${APP_NAME}:${VERSION}"

    # Wait for green environment to be ready
    info "Waiting for green environment to initialize..."
    sleep 30

    # Health check green environment
    if ! health_check $GREEN_PORT $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL "green environment"; then
        error "Green environment health check failed"
        docker stop fes_crm_green 2>/dev/null || true
        docker rm fes_crm_green 2>/dev/null || true
        exit 1
    fi

    # Run smoke tests on green environment
    if ! run_smoke_tests $GREEN_PORT "green environment"; then
        error "Green environment smoke tests failed"
        docker stop fes_crm_green 2>/dev/null || true
        docker rm fes_crm_green 2>/dev/null || true
        exit 1
    fi

    # Step 2: Switch traffic
    switch_traffic "${APP_NAME}:${VERSION}"

    # Final health check on production port
    if ! health_check $BLUE_PORT $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL "production environment"; then
        error "Production environment health check failed"

        # Emergency rollback
        if [[ -d "$BACKUP_DIR" ]]; then
            warn "Performing emergency rollback..."
            docker compose -f docker-compose.prod.yml down --remove-orphans
            cp -r "$BACKUP_DIR"/* .
            docker compose -f docker-compose.prod.yml up -d

            if health_check $BLUE_PORT 10 5 "rollback environment"; then
                log "Emergency rollback successful"
            else
                error "Emergency rollback failed - manual intervention required"
            fi
        fi
        exit 1
    fi

    # Final smoke tests
    if ! run_smoke_tests $BLUE_PORT "production environment"; then
        warn "Production smoke tests failed, but deployment will continue"
    fi

    # Save deployment info
    echo "$VERSION" > .version
    echo "$(date)" > .deployment-time
    echo "$BACKUP_TIMESTAMP" > .last-backup

    # Cleanup old backups (keep last 3)
    find /root -name "${APP_NAME}-backup-*" -type d | sort | head -n -3 | xargs rm -rf 2>/dev/null || true

    # Cleanup Docker resources
    info "Cleaning up Docker resources..."
    docker system prune -f > /dev/null 2>&1 || true
    docker image prune -f > /dev/null 2>&1 || true

    # Display final status
    log "Zero-downtime deployment completed successfully!"
    log "New version $VERSION is now live on port $BLUE_PORT"

    info "Deployment Summary:"
    echo "  Previous version: ${CURRENT_VERSION:-none}"
    echo "  New version: $VERSION"
    echo "  Backup location: $BACKUP_DIR"
    echo "  Deployment time: $(date)"

    # Show running containers
    info "Running containers:"
    docker compose -f docker-compose.prod.yml ps
}

# Run main function
main "$@"
