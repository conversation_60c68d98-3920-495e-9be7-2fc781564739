#!/bin/bash

# Simple Production Deployment Script for FES CRM Backend
# This script provides a more reliable deployment without complex blue-green setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_NAME="fes-crm-backend"
MAIN_PORT=5000
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10
DEPLOYMENT_TIMEOUT=600

# Show help
show_help() {
    cat << EOF
Simple Production Deployment Script for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Docker image version/tag to deploy
    -f, --force             Force deployment without confirmation
    -h, --help              Show this help message

EXAMPLES:
    $0 -v latest                    # Deploy latest version
    $0 -v abc123 -f                 # Force deploy specific commit

EOF
}

# Parse command line arguments
VERSION=""
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$VERSION" ]]; then
    error "Version is required. Use -v or --version to specify."
    show_help
    exit 1
fi

# Health check function
health_check() {
    local port=$1
    local retries=${2:-10}
    local interval=${3:-5}
    local service_name=${4:-"service"}

    info "Running health checks for $service_name on port $port..."

    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
            log "Health check passed for $service_name on port $port (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed for $service_name on port $port (attempt $i/$retries)"
        sleep $interval
    done

    error "Health check failed after $retries attempts for $service_name on port $port"
    return 1
}

# Network cleanup function
cleanup_networks() {
    info "Cleaning up conflicting Docker networks..."
    
    # Remove any conflicting networks
    local conflicting_networks=(
        "clones-backend_clone_backend"
        "fes-crm-backend_fes_crm_network"
    )
    
    for network in "${conflicting_networks[@]}"; do
        if docker network ls --format "{{.Name}}" | grep -q "^${network}$"; then
            warn "Removing conflicting network: $network"
            docker network rm "$network" 2>/dev/null || true
        fi
    done
    
    log "Network cleanup completed"
}

# Main deployment function
main() {
    log "Starting simple production deployment for $APP_NAME version $VERSION"
    
    # Check if we're in the right directory
    if [[ ! -f "docker-compose.prod.yml" ]]; then
        error "docker-compose.prod.yml not found. Please run this script from the project root."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running or not accessible"
        exit 1
    fi
    
    # Check if required image exists
    if ! docker image inspect "${APP_NAME}:${VERSION}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${VERSION} not found"
        exit 1
    fi
    
    # Confirmation prompt
    if [[ "$FORCE" != "true" ]]; then
        echo
        info "Deployment Configuration:"
        echo "  Version: $VERSION"
        echo "  Port: $MAIN_PORT"
        echo "  Health checks: $HEALTH_CHECK_RETRIES retries every ${HEALTH_CHECK_INTERVAL}s"
        echo
        read -p "Continue with deployment? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            warn "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Get current deployment info
    CURRENT_VERSION=""
    if [[ -f ".version" ]]; then
        CURRENT_VERSION=$(cat .version)
        info "Current version: $CURRENT_VERSION"
    else
        info "No previous deployment found"
    fi
    
    # Create backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_DIR="/root/${APP_NAME}-backup-${BACKUP_TIMESTAMP}"
    
    # Backup current deployment (including .env)
    if [[ -n "$CURRENT_VERSION" ]]; then
        info "Creating backup of current deployment..."
        cp -r . "$BACKUP_DIR"
        log "Backup created: $BACKUP_DIR"
    fi
    
    # Clean up conflicting networks
    cleanup_networks
    
    # Update compose file with new version
    info "Updating Docker Compose configuration..."
    sed -i "s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g" docker-compose.prod.yml
    
    # Stop current deployment
    info "Stopping current deployment..."
    docker compose -f docker-compose.prod.yml down --remove-orphans
    
    # Wait for cleanup
    sleep 5
    
    # Start new deployment
    info "Starting new deployment..."
    docker compose -f docker-compose.prod.yml up -d
    
    # Wait for startup
    info "Waiting for application to start..."
    sleep 15
    
    # Health check
    if health_check $MAIN_PORT $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL "production environment"; then
        log "Deployment successful!"
        
        # Save deployment info
        echo "$VERSION" > .version
        echo "$(date)" > .deployment-time
        echo "$BACKUP_TIMESTAMP" > .last-backup
        
        # Show final status
        info "Final deployment status:"
        docker compose -f docker-compose.prod.yml ps
        
        # Cleanup old backups (keep last 3)
        find /root -name "${APP_NAME}-backup-*" -type d | sort | head -n -3 | xargs rm -rf 2>/dev/null || true
        
        log "Deployment completed successfully!"
        log "New version $VERSION is now live on port $MAIN_PORT"
        
    else
        error "Deployment health check failed"
        
        # Rollback if backup exists
        if [[ -d "$BACKUP_DIR" ]]; then
            warn "Performing rollback..."
            docker compose -f docker-compose.prod.yml down --remove-orphans
            
            # Restore from backup
            cp -r "$BACKUP_DIR"/* .
            
            # Start restored version
            docker compose -f docker-compose.prod.yml up -d
            
            # Health check rollback
            sleep 15
            if health_check $MAIN_PORT 10 5 "rollback environment"; then
                log "Rollback successful"
            else
                error "Rollback failed - manual intervention required"
            fi
        fi
        
        exit 1
    fi
}

# Run main function
main "$@"
