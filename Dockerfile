FROM node:20-alpine

# Set Node Environment
ENV BACKEND_PORT=5000

# Install required dependencies
RUN apk add --no-cache openssl


RUN apk add --no-cache bash
SHELL ["/bin/bash", "-c"]


WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./
COPY .npmrc ./

# Install dependencies
RUN npm install

# Build the app
COPY . .
# Expose the port
EXPOSE 5000
ENV NODE_ENV=production

RUN chmod +x entrypoint.sh
RUN chmod 755 /usr/src/app/entrypoint.sh

RUN ls -l /usr/src/app/entrypoint.sh

# Run the entrypoint script
CMD ["sh", "-c", "chmod +x /usr/src/app/entrypoint.sh && /usr/src/app/entrypoint.sh"]