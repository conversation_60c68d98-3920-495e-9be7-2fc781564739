#!/bin/sh

# Production entrypoint script
set -e

echo "🚀 Starting FES CRM Backend in Production Mode..."

# Wait for database to be ready (if needed)
echo "⏳ Waiting for database connection..."

# Run database migrations
echo "📊 Running database migrations..."
npx prisma migrate deploy

# Seed the database (optional - remove if not needed in production)
#echo "🌱 Seeding database..."
#npx prisma db seed

echo "✅ Database setup complete!"

# Start the application
echo "🎯 Starting the application..."
exec node dist/src/main.js
