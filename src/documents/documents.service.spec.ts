import { HttpException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from 'src/prisma/prisma.service';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { GetDocumentDto } from './dto/get-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';

describe('DocumentsService', () => {
  let service: DocumentsService;
  let prisma: PrismaService;

  const mockDocument: GetDocumentDto = {
    id: '1',
    type: 'PASSPORT',
    fileUrl: 'file1.pdf',
    studentId: 'student1',
    status: 'PENDING',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPrisma = {
    document: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentsService,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();

    service = module.get<DocumentsService>(DocumentsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => jest.clearAllMocks());

  describe('createDocument', () => {
    it('should create a new document', async () => {
      const dto: CreateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = {
        filename: 'file1.pdf',
        mimetype: 'application/pdf',
        size: 1024 * 1024 // 1MB
      } as Express.Multer.File;
      mockPrisma.document.create.mockResolvedValue(mockDocument);

      const result = await service.createDocument(dto, file);
      expect(result).toEqual(mockDocument);
      expect(mockPrisma.document.create).toHaveBeenCalledWith({
        data: {
          type: dto.type,
          fileUrl: file.filename,
          studentId: dto.studentId,
          comment: dto.comment,
          applicationId: dto.applicationId,
        },
      });
    });

    it('should throw NotFoundException if student does not exist', async () => {
      const dto: CreateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = {
        filename: 'file1.pdf',
        mimetype: 'application/pdf',
        size: 1024 * 1024 // 1MB
      } as Express.Multer.File;
      mockPrisma.document.create.mockRejectedValue(
        new PrismaClientKnownRequestError('Student not found', {
          code: 'P2003',
          clientVersion: '1',
        }),
      );

      await expect(service.createDocument(dto, file)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findDocumentById', () => {
    it('should return a document by ID', async () => {
      mockPrisma.document.findUnique.mockResolvedValue(mockDocument);

      const result = await service.findDocumentById('1');
      expect(result).toEqual(mockDocument);
    });

    it('should throw NotFoundException if document not found', async () => {
      mockPrisma.document.findUnique.mockResolvedValue(null);

      await expect(service.findDocumentById('1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findAllDocumentsByStudentId', () => {
    it('should return all documents for a student', async () => {
      mockPrisma.document.findMany.mockResolvedValue([mockDocument]);
      mockPrisma.document.count.mockResolvedValue(1);

      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      const result = await service.findAllDocumentsByStudentId('student1', mockPageOptions);

      expect(result.data).toEqual([mockDocument]);
      expect(result.meta).toEqual({
        itemCount: 1,
        page: 1,
        take: 10,
        pageCount: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });
    });

    it('should throw HttpException if an error occurs', async () => {
      mockPrisma.document.findMany.mockRejectedValue(
        new Error('Database error'),
      );

      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      await expect(
        service.findAllDocumentsByStudentId('student1', mockPageOptions),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('updateDocument', () => {
    it('should update an existing document', async () => {
      const dto: UpdateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = {
        filename: 'file2.pdf',
        mimetype: 'application/pdf',
        size: 1024 * 1024 // 1MB
      } as Express.Multer.File;
      mockPrisma.document.findUnique.mockResolvedValue(mockDocument);
      mockPrisma.document.update.mockResolvedValue(mockDocument);

      const result = await service.updateDocument('1', dto, file);
      expect(result).toEqual(mockDocument);
      expect(mockPrisma.document.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          type: dto.type,
          studentId: dto.studentId,
          fileUrl: file.filename,
        },
      });
    });

    it('should throw NotFoundException if document not found', async () => {
      const dto: UpdateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      mockPrisma.document.findUnique.mockResolvedValue(null);

      await expect(service.updateDocument('1', dto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw NotFoundException if student does not exist', async () => {
      const dto: UpdateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      mockPrisma.document.findUnique.mockResolvedValue(mockDocument);
      mockPrisma.document.update.mockRejectedValue(
        new PrismaClientKnownRequestError('Student not found', {
          code: 'P2003',
          clientVersion: '1',
        }),
      );

      await expect(service.updateDocument('1', dto)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('deleteDocument', () => {
    it('should delete a document', async () => {
      mockPrisma.document.findUnique.mockResolvedValue(mockDocument);
      mockPrisma.document.delete.mockResolvedValue(mockDocument);

      const result = await service.deleteDocument('1');
      expect(result).toEqual(mockDocument);
      expect(mockPrisma.document.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      });
    });

    it('should throw NotFoundException if document not found', async () => {
      mockPrisma.document.findUnique.mockResolvedValue(null);

      await expect(service.deleteDocument('1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});