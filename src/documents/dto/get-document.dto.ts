import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { DocumentStatus } from '@prisma/client';

export class GetDocumentDto {
  @ApiProperty({
    description: 'Unique identifier for the document',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Upload date of the Document',
    required: true,
    type: Date,
  })
  @IsNotEmpty()
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the Document was updated',
    required: true,
    type: Date,
  })
  @IsNotEmpty()
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'File URL of the Document',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  fileUrl: string;

  @ApiProperty({
    description: 'Type of the Document being queried',
    required: true,
    type: String,
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'ID of the Student, this Document belongs to',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  studentId?: string;

  @ApiProperty({
    description: 'Optional comment about the document',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiProperty({
    description: 'Status of the document',
    required: true,
    enum: DocumentStatus,
  })
  status: DocumentStatus;

  @ApiProperty({
    description: 'Reason for rejection if status is REJECTED',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  rejectionReason?: string;

  @ApiProperty({
    description: 'ID of the user who approved/rejected the document',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  approvedById?: string;

  @ApiProperty({
    description: 'Date when the document was approved/rejected',
    required: false,
    type: Date,
  })
  @IsOptional()
  @IsDate()
  approvedAt?: Date;
}
