import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { AuthGuard } from './auth.guard';
import { XConfigService } from '../../common/services/xconfig.service';
import { PrismaService } from '../../prisma/prisma.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { XRequest } from '../XRequest';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';

@Injectable()
export class PermissionsGuard extends AuthGuard implements CanActivate {
  constructor(
    readonly reflector: Reflector,
    readonly configService: XConfigService,
    private readonly prisma: PrismaService,
  ) {
    super(reflector, configService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    const request = context.switchToHttp().getRequest<XRequest>();

    // if auth enabled, validate the authentications
    if (this.configService.isAuthEnabled) await super.canActivate(context);

    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    return this.validateAuth(request, requiredPermissions);
  }

  async validateAuth(request: XRequest, requiredPermissions: string[] = []) {
    const isAuthEnabled = this.configService.isAuthEnabled;
    if (!isAuthEnabled) {
      const user = await this.prisma.user.findUnique({
        where: { id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6' },
        include: {
          roles: {
            include: {
              permissions: true,
            },
          },
        },
      });

      request.user = {
        ...user,
        roles: user?.roles?.map((role) => role.name),
        permissions: user?.roles?.flatMap((role) =>
          role.permissions.map((perm) => perm.name),
        ),
      };
      return true;
    }

    if (!(await AuthGuard.isAuthenticated(request))) return false;
    if (!request.token) return false;

    try {
      const user = await this.prisma.user.findUnique({
        where: { id: request.token.id },
        include: {
          roles: {
            include: {
              permissions: true,
            },
          },
        },
      });
      request.user = {
        ...user,
        roles: user?.roles?.map((role) => role.name),
        permissions: user?.roles?.flatMap((role) =>
          role.permissions.map((perm) => perm.name),
        ),
      };
    } catch (e) {
      if (e instanceof PrismaClientKnownRequestError) {
        if (e.code === 'P2025') {
          if (!request.token.id) throw new Error('User Id not found');
        } else {
          throw e;
        }
      } else {
        throw e;
      }
    }
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    // check if the permissions include * to allow all permissions to pass
    if (request.user.permissions.includes('*')) {
      return true;
    }

    requiredPermissions = requiredPermissions.map((permission) =>
      permission.toUpperCase(),
    );
    const userPermissionsUpperCase = request.user.permissions
      ? request.user.permissions.map((r) => r.toUpperCase())
      : [];
    // Perform the comparison
    return requiredPermissions.some((permission) => {
      return userPermissionsUpperCase.includes(permission);
    });
  }
}
