import {
  ConflictException,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from '../prisma/prisma.service';
import { RolesService } from './roles.service';

describe('RolesService', () => {
  let rolesService: RolesService;
  let prismaService: PrismaService;

  // Create a mock Prisma service with jest.fn() for each method used
  const mockPrisma = {
    role: {
      findUnique: jest.fn(),
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesService,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();

    rolesService = module.get<RolesService>(RolesService);
    prismaService = module.get<PrismaService>(PrismaService);

    jest.clearAllMocks();
  });

  // --------------------------
  // Test Cases for createANewRole
  // --------------------------
  describe('createANewRole', () => {
    it('should throw ConflictException if role already exists', async () => {
      // Simulate an existing role
      mockPrisma.role.findUnique.mockResolvedValue({ name: 'ADMIN' });
      const createRoleDto = { name: 'ADMIN', description: 'Admin role' };

      await expect(rolesService.createANewRole(createRoleDto)).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { name: createRoleDto.name },
      });
    });

    it('should create and return a new role if it does not exist', async () => {
      // No role exists with the given name
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const newRole = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        permissions: [],
        users: [],
      };
      mockPrisma.role.create.mockResolvedValue(newRole);
      const createRoleDto = { name: 'ADMIN', description: 'Admin role' };

      const result = await rolesService.createANewRole(createRoleDto);
      expect(result).toEqual(newRole);
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { name: createRoleDto.name },
      });
      expect(mockPrisma.role.create).toHaveBeenCalledWith({
        data: { ...createRoleDto },
        include: {
          permissions: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          }
        },
      });
    });

    it('should throw ConflictException for Prisma P2002 error', async () => {
      // Simulate Prisma known error P2002 (unique constraint violation)
      const prismaError = new PrismaClientKnownRequestError('Error', {
        code: 'P2002',
        clientVersion: 'clientVersion',
      });
      mockPrisma.role.findUnique.mockResolvedValue(null);
      mockPrisma.role.create.mockRejectedValue(prismaError);
      const createRoleDto = { name: 'ADMIN', description: 'Admin role' };

      await expect(rolesService.createANewRole(createRoleDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should throw HttpException for other errors', async () => {
      // Simulate a generic error
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const error = new Error('General error');
      mockPrisma.role.create.mockRejectedValue(error);
      const createRoleDto = { name: 'ADMIN', description: 'Admin role' };

      await expect(rolesService.createANewRole(createRoleDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  // --------------------------
  // Test Cases for getAllRoles
  // --------------------------
  describe('getAllRoles', () => {
    it('should return a PageDto with roles data and meta', async () => {
      const roles = [
        {
          id: '1',
          name: 'ADMIN',
          description: 'Admin role',
          users: [],
          permissions: [],
        },
      ];
      mockPrisma.role.findMany.mockResolvedValue(roles);
      mockPrisma.role.count.mockResolvedValue(1);
      // Include the required 'page' property for PageOptionsDto
      const pageOptionsDto = { skip: 0, take: 10, page: 1 };

      const result = await rolesService.getAllRoles(pageOptionsDto);

      expect(result.data).toEqual(roles);
      expect(result.meta.itemCount).toEqual(1);
      expect(mockPrisma.role.findMany).toHaveBeenCalledWith({
        take: 10,
        skip: 0,
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: { id: true, name: true, description: true, group: true },
          },
        },
      });
      expect(mockPrisma.role.count).toHaveBeenCalled();
    });

    it('should throw HttpException if an error occurs during retrieval', async () => {
      const error = new Error('Find many error');
      mockPrisma.role.findMany.mockRejectedValue(error);
      const pageOptionsDto = { skip: 0, take: 10, page: 1 };

      await expect(rolesService.getAllRoles(pageOptionsDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  // --------------------------
  // Test Cases for getARole
  // --------------------------
  describe('getARole', () => {
    it('should return a role when found', async () => {
      const role = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        users: [],
        permissions: [],
      };
      mockPrisma.role.findUnique.mockResolvedValue(role);
      const id = '1';

      const result = await rolesService.getARole(id);

      expect(result).toEqual(role);
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: { id: true, name: true, description: true, group: true },
          },
        },
      });
    });

    it('should throw NotFoundException if the role is not found', async () => {
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const id = '1';

      await expect(rolesService.getARole(id)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw HttpException for other errors', async () => {
      const error = new Error('General error');
      mockPrisma.role.findUnique.mockRejectedValue(error);
      const id = '1';

      await expect(rolesService.getARole(id)).rejects.toThrow(HttpException);
    });
  });

  // --------------------------
  // Test Cases for updateARole
  // --------------------------
  describe('updateARole', () => {
    it('should throw ConflictException if a role with the new name exists', async () => {
      // Simulate conflict by finding an existing role with the same name
      mockPrisma.role.findUnique.mockResolvedValue({
        id: 'existing',
        name: 'ADMIN',
      });
      const id = '1';
      const updateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };

      await expect(rolesService.updateARole(id, updateRoleDto)).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { name: updateRoleDto.name },
      });
    });

    it('should update and return a role successfully when no conflict exists', async () => {
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const updatedRole = {
        id: '1',
        name: 'ADMIN',
        description: 'Updated description',
        users: [],
        permissions: [],
      };
      mockPrisma.role.update.mockResolvedValue(updatedRole);
      const id = '1';
      const updateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };

      const result = await rolesService.updateARole(id, updateRoleDto);

      expect(result).toEqual(updatedRole);
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { name: updateRoleDto.name },
      });
      expect(mockPrisma.role.update).toHaveBeenCalledWith({
        where: { id },
        data: { ...updateRoleDto },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: { id: true, name: true, description: true, group: true },
          },
        },
      });
    });

    it('should throw ConflictException for a Prisma P2002 error during update', async () => {
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const prismaError = new PrismaClientKnownRequestError('Error', {
        code: 'P2002',
        clientVersion: 'clientVersion',
      });
      mockPrisma.role.update.mockRejectedValue(prismaError);
      const id = '1';
      const updateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };

      await expect(rolesService.updateARole(id, updateRoleDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should throw HttpException for other errors during update', async () => {
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const error = new Error('General error');
      mockPrisma.role.update.mockRejectedValue(error);
      const id = '1';
      const updateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };

      await expect(rolesService.updateARole(id, updateRoleDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  // --------------------------
  // Test Cases for deleteARole
  // --------------------------
  describe('deleteARole', () => {
    it('should delete and return the role if it exists', async () => {
      const role = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        users: [],
        permissions: [],
      };
      mockPrisma.role.findUnique.mockResolvedValue(role);
      mockPrisma.role.delete.mockResolvedValue(role);
      const id = '1';

      const result = await rolesService.deleteARole(id);

      expect(result).toEqual(role);
      expect(mockPrisma.role.findUnique).toHaveBeenCalledWith({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: { id: true, name: true, description: true, group: true },
          },
        },
      });
      expect(mockPrisma.role.delete).toHaveBeenCalledWith({ where: { id } });
    });

    it('should throw NotFoundException if the role does not exist', async () => {
      mockPrisma.role.findUnique.mockResolvedValue(null);
      const id = '1';

      await expect(rolesService.deleteARole(id)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw HttpException for errors during deletion', async () => {
      const role = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        users: [],
        permissions: [],
      };
      mockPrisma.role.findUnique.mockResolvedValue(role);
      const error = new Error('General error');
      mockPrisma.role.delete.mockRejectedValue(error);
      const id = '1';

      await expect(rolesService.deleteARole(id)).rejects.toThrow(HttpException);
    });
  });
});
