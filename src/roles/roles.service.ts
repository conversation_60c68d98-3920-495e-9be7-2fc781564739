import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PageDto } from '../common/dtos/page.dto';
import { PageMetaDto } from '../common/dtos/pageMeta.dto';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { GetARoleDto } from './dto/get-a-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Injectable()
export class RolesService {
  constructor(private prisma: PrismaService) {}

  async createANewRole(createRoleDto: CreateRoleDto): Promise<GetARoleDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { name: createRoleDto.name },
      });

      if (role) {
        throw new ConflictException('Role already exists.');
      }

      const newRole = await this.prisma.role.create({
        data: { ...createRoleDto },
        include: {
          // Because initially permissions and users arrays will be empty
          permissions: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      return newRole;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Role already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllRoles(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<GetARoleDto>> {
    try {
      const { skip, take = 10 } = pageOptionsDto;

      const [data, itemCount] = await Promise.all([
        this.prisma.role.findMany({
          take: take,
          skip: skip,
          select: {
            id: true,
            name: true,
            description: true,
            users: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
                employee: {
                  select: {
                    id: true,
                  },
                },
              },
            },
            permissions: {
              select: {
                id: true,
                name: true,
                description: true,
                group: true,
              },
            },
          },
        }),
        this.prisma.role.count(),
      ]);

      const meta = new PageMetaDto({ itemCount, pageOptionsDto });
      return new PageDto<GetARoleDto>(data, meta);
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getARole(id: string): Promise<GetARoleDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: {
              id: true,
              name: true,
              description: true,
              group: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException('Role does not exist.');
      }

      return role;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getCounsellors(): Promise<GetARoleDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { name: 'COUNSELLOR' },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: {
              id: true,
              name: true,
              description: true,
              group: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException('Role does not exist.');
      }

      return role;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateARole(
    id: string,
    updateRoleDto: UpdateRoleDto,
  ): Promise<GetARoleDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { name: updateRoleDto.name },
      });

      if (role) {
        throw new ConflictException('Role already exists.');
      }

      return await this.prisma.role.update({
        where: { id },
        data: { ...updateRoleDto },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: {
              id: true,
              name: true,
              description: true,
              group: true,
            },
          },
        },
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Role already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteARole(id: string) {
    try {
      const role = await this.prisma.role.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: {
              id: true,
              name: true,
              description: true,
              group: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException('Role does not exist.');
      }

      await this.prisma.role.delete({
        where: { id },
      });
      return role;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updatePermissions(
    roleId: string,
    permissions: string[],
  ): Promise<GetARoleDto> {
    try {
      const role = await this.prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException('Role does not exist.');
      }

      const updatedRole = await this.prisma.role.update({
        where: { id: roleId },
        data: {
          permissions: {
            set: permissions.map((pid) => ({ id: pid })),
          },
        },
        select: {
          id: true,
          name: true,
          description: true,
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              employee: {
                select: {
                  id: true,
                },
              },
            },
          },
          permissions: {
            select: {
              id: true,
              name: true,
              description: true,
              group: true,
            },
          },
        },
      });

      return updatedRole;
    } catch (error) {
      console.log('message:');
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(
            'One or more permission IDs are invalid or not found.',
          );
        }
      } else if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
