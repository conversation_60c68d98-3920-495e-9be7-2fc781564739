import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PermissionDto } from './permission.dto';
import { UserDto } from './user.dto';

export class GetARoleDto {
  @ApiProperty({
    description: 'The id of the Role',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'The name of the Role',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'The description of the Role',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'The permissions granted to a Role',
    required: false,
    type: [PermissionDto],
  })
  permissions?: PermissionDto[];

  @ApiProperty({
    description: 'The Users associated with the Role',
    required: false,
    type: [UserDto],
  })
  users?: UserDto[];
}
