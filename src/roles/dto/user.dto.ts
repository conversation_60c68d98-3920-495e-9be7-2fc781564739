import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '@prisma/client';
import { IsNotEmpty, IsString } from 'class-validator';
import { EmployeeDto } from './employee.dto';

export class UserDto {
  @ApiProperty({
    description: 'The id of the user',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'User ID', type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'User Email', type: String })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User Status',
    enum: UserStatus,
  })
  @IsNotEmpty()
  status: UserStatus;

  @ApiProperty({
    description: 'The Employee associated with the User',
    required: true,
    type: EmployeeDto,
  })
  employee: EmployeeDto;
}
