import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class PermissionDto {
  @ApiProperty({
    description: 'The name of the Permission',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'The id of the Permission',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Description of the Role Permissions',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Role group of Permissions',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  group?: string;
}
