import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PageDto } from 'src/common/dtos/page.dto';
import { Permissions } from '../Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from '../Auth/decorators/roles-permissions.swagger.decorator';
import { Roles } from '../Auth/decorators/roles.decorator';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { CreateRoleDto } from './dto/create-role.dto';
import { GetARoleDto } from './dto/get-a-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RolesService } from './roles.service';

@ApiTags('roles')
@ApiBearerAuth()
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @Permissions('create:roles')
  @ApiResponse({
    status: 200,
    description: 'A new Role has been created successfully.',
    type: [GetARoleDto],
  })
  async createANewRole(
    @Body() createRoleDto: CreateRoleDto,
  ): Promise<GetARoleDto> {
    try {
      return this.rolesService.createANewRole(createRoleDto);
    } catch (error) {
      return error;
    }
  }

  @Get()
  @Permissions('read:roles')
  @ApiResponse({
    status: 200,
    description: 'All Roles have been retrieved successfully.',
    type: [GetARoleDto],
  })
  async getAllRoles(
    @Query() pageOptions: PageOptionsDto,
  ): Promise<PageDto<GetARoleDto>> {
    try {
      return this.rolesService.getAllRoles(pageOptions);
    } catch (error) {
      return error;
    }
  }

  @Get('counsellors')
  @Permissions('read:roles')
  @ApiResponse({
    status: 200,
    description: 'A Role has been retrieved successfully.',
    type: GetARoleDto,
  })
  async getCounsellors(): Promise<GetARoleDto> {
    try {
      return this.rolesService.getCounsellors();
    } catch (error) {
      return error;
    }
  }

  @Get(':id')
  @Permissions('read:roles')
  @ApiResponse({
    status: 200,
    description: 'A Role has been retrieved successfully.',
    type: GetARoleDto,
  })
  async getARole(@Param('id') id: string): Promise<GetARoleDto> {
    try {
      return this.rolesService.getARole(id);
    } catch (error) {
      return error;
    }
  }

  @Patch(':id')
  @Permissions('update:roles')
  @ApiResponse({
    status: 200,
    description: 'A Role has been updated successfully.',
    type: GetARoleDto,
  })
  async updateARole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<GetARoleDto> {
    try {
      return this.rolesService.updateARole(id, updateRoleDto);
    } catch (error) {
      return error;
    }
  }

  @Delete(':id')
  @Permissions('delete:roles')
  @ApiResponse({
    status: 200,
    description: 'A Role has been deleted successfully.',
    type: GetARoleDto,
  })
  async deleteARole(@Param('id') id: string): Promise<any> {
    try {
      return this.rolesService.deleteARole(id);
    } catch (error) {
      return error;
    }
  }

  @Post(':id/permissions')
  @Permissions('update:roles')
  @ApiResponse({
    status: 200,
    description: 'A Role has been deleted successfully.',
    type: GetARoleDto,
  })
  async updatePermissions(
    @Param('id') id: string,
    @Body() permissions: string[],
  ): Promise<GetARoleDto> {
    try {
      return this.rolesService.updatePermissions(id, permissions);
    } catch (error) {
      return error;
    }
  }
}
