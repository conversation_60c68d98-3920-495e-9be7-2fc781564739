import { Test, TestingModule } from '@nestjs/testing';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';

describe('RolesController', () => {
  let rolesController: RolesController;
  let rolesService: RolesService;

  // Create a mock for the RolesService including updatePermissions
  const mockRolesService = {
    createANewRole: jest.fn(),
    getAllRoles: jest.fn(),
    getARole: jest.fn(),
    updateARole: jest.fn(),
    deleteARole: jest.fn(),
    updatePermissions: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RolesController],
      providers: [{ provide: RolesService, useValue: mockRolesService }],
    }).compile();

    rolesController = module.get<RolesController>(RolesController);
    rolesService = module.get<RolesService>(RolesService);
    jest.clearAllMocks();
  });

  // --------------------------
  // Test Cases for createANewRole (POST /roles)
  // --------------------------
  describe('createANewRole', () => {
    it('should create and return a new role', async () => {
      const createRoleDto: CreateRoleDto = {
        name: 'ADMIN',
        description: 'Admin role',
      };
      const roleResponse = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        permissions: [],
        users: [],
      };

      mockRolesService.createANewRole.mockResolvedValue(roleResponse);

      const result = await rolesController.createANewRole(createRoleDto);
      expect(result).toEqual(roleResponse);
      expect(mockRolesService.createANewRole).toHaveBeenCalledWith(
        createRoleDto,
      );
    });

    it('should throw an error if the service throws an error', async () => {
      const createRoleDto: CreateRoleDto = {
        name: 'ADMIN',
        description: 'Admin role',
      };
      const error = new Error('Service error');
      mockRolesService.createANewRole.mockRejectedValue(error);

      await expect(
        rolesController.createANewRole(createRoleDto),
      ).rejects.toThrow('Service error');
    });
  });

  // --------------------------
  // Test Cases for getAllRoles (GET /roles)
  // --------------------------
  describe('getAllRoles', () => {
    it('should return all roles with pagination', async () => {
      const pageOptionsDto: PageOptionsDto = { skip: 0, take: 10, page: 1 };
      const rolesPageResponse = {
        data: [
          {
            id: '1',
            name: 'ADMIN',
            description: 'Admin role',
            permissions: [],
            users: [],
          },
        ],
        meta: { itemCount: 1 },
      };

      mockRolesService.getAllRoles.mockResolvedValue(rolesPageResponse);

      const result = await rolesController.getAllRoles(pageOptionsDto);
      expect(result).toEqual(rolesPageResponse);
      expect(mockRolesService.getAllRoles).toHaveBeenCalledWith(pageOptionsDto);
    });

    it('should throw an error if the service throws an error', async () => {
      const pageOptionsDto: PageOptionsDto = { skip: 0, take: 10, page: 1 };
      const error = new Error('Service error');
      mockRolesService.getAllRoles.mockRejectedValue(error);

      await expect(rolesController.getAllRoles(pageOptionsDto)).rejects.toThrow(
        'Service error',
      );
    });
  });

  // --------------------------
  // Test Cases for getARole (GET /roles/:id)
  // --------------------------
  describe('getARole', () => {
    it('should return a role by id', async () => {
      const id = '1';
      const roleResponse = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        permissions: [],
        users: [],
      };
      mockRolesService.getARole.mockResolvedValue(roleResponse);

      const result = await rolesController.getARole(id);
      expect(result).toEqual(roleResponse);
      expect(mockRolesService.getARole).toHaveBeenCalledWith(id);
    });

    it('should throw an error if the service throws an error', async () => {
      const id = '1';
      const error = new Error('Service error');
      mockRolesService.getARole.mockRejectedValue(error);

      await expect(rolesController.getARole(id)).rejects.toThrow(
        'Service error',
      );
    });
  });

  // --------------------------
  // Test Cases for updateARole (PATCH /roles/:id)
  // --------------------------
  describe('updateARole', () => {
    it('should update and return the role', async () => {
      const id = '1';
      const updateRoleDto: UpdateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };
      const updatedRole = {
        id: '1',
        name: 'ADMIN',
        description: 'Updated description',
        permissions: [],
        users: [],
      };
      mockRolesService.updateARole.mockResolvedValue(updatedRole);

      const result = await rolesController.updateARole(id, updateRoleDto);
      expect(result).toEqual(updatedRole);
      expect(mockRolesService.updateARole).toHaveBeenCalledWith(
        id,
        updateRoleDto,
      );
    });

    it('should throw an error if the service throws an error', async () => {
      const id = '1';
      const updateRoleDto: UpdateRoleDto = {
        name: 'ADMIN',
        description: 'Updated description',
      };
      const error = new Error('Service error');
      mockRolesService.updateARole.mockRejectedValue(error);

      await expect(
        rolesController.updateARole(id, updateRoleDto),
      ).rejects.toThrow('Service error');
    });
  });

  // --------------------------
  // Test Cases for deleteARole (DELETE /roles/:id)
  // --------------------------
  describe('deleteARole', () => {
    it('should delete and return the role', async () => {
      const id = '1';
      const deletedRole = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        permissions: [],
        users: [],
      };
      mockRolesService.deleteARole.mockResolvedValue(deletedRole);

      const result = await rolesController.deleteARole(id);
      expect(result).toEqual(deletedRole);
      expect(mockRolesService.deleteARole).toHaveBeenCalledWith(id);
    });

    it('should throw an error if the service throws an error', async () => {
      const id = '1';
      const error = new Error('Service error');
      mockRolesService.deleteARole.mockRejectedValue(error);

      await expect(rolesController.deleteARole(id)).rejects.toThrow(
        'Service error',
      );
    });
  });

  // --------------------------
  // Test Cases for updatePermissions (POST /roles/:id/permissions)
  // --------------------------
  describe('updatePermissions', () => {
    it('should update and return the role with updated permissions', async () => {
      const id = '1';
      const permissions = ['PERM1', 'PERM2'];
      const updatedRole = {
        id: '1',
        name: 'ADMIN',
        description: 'Admin role',
        permissions,
        users: [],
      };
      mockRolesService.updatePermissions.mockResolvedValue(updatedRole);

      const result = await rolesController.updatePermissions(id, permissions);
      expect(result).toEqual(updatedRole);
      expect(mockRolesService.updatePermissions).toHaveBeenCalledWith(
        id,
        permissions,
      );
    });

    it('should throw an error if the service throws an error', async () => {
      const id = '1';
      const permissions = ['PERM1', 'PERM2'];
      const error = new Error('Service error');
      mockRolesService.updatePermissions.mockRejectedValue(error);

      await expect(
        rolesController.updatePermissions(id, permissions),
      ).rejects.toThrow('Service error');
    });
  });
});
