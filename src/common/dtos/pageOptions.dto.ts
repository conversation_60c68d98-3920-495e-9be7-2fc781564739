import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, <PERSON>, Min } from 'class-validator';
import { Type } from 'class-transformer';

export enum PageOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class PageOptionsDto {
  @ApiPropertyOptional({
    enum: PageOrder,
    default: PageOrder.ASC,
  })
  @IsOptional()
  @IsEnum(PageOrder)
  readonly order?: PageOrder = PageOrder.ASC;

  @ApiPropertyOptional({
    minimum: 1,
    default: 1,
    type: Number,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  page: number = 1;

  @ApiPropertyOptional({
    minimum: 1,
    maximum: 50,
    default: 50,
    type: Number,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  take: number = 50;

  get skip(): number {
    return (this.page - 1) * this.take || 0;
  }
}
