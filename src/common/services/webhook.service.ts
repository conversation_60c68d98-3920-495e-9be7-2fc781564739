import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { AxiosResponse } from 'axios';

@Injectable()
export class WebhookService {
  constructor(private readonly httpService: HttpService) {}

  async triggerWebhook(
    url: string,
    payload: Record<string, any>,
    headers: Record<string, string> = {},
  ): Promise<AxiosResponse> {
    try {
      const response = await this.httpService.axiosRef.post(url, payload, {
        headers,
      });
      return response;
    } catch (error) {
      throw new Error(`Failed to trigger webhook: ${error.message}`);
    }
  }
}
