import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { XRequest } from 'src/Auth/XRequest';
import { AddAStudentDto } from './dto/add-a-student.dto';
import { AddStudentLeadDto } from './dto/add-student-lead.dto';
import { GetAStudentDto } from './dto/get-a-student.dto';
import { UpdateAStudentDto } from './dto/update-a-student.dto';
import { StudentsService } from './students.service';
import { GetLeadsQueryDto } from './dto/get-leads-query.dto';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { PageDto } from '../common/dtos/page.dto';

@ApiBearerAuth()
@Controller('students')
@ApiTags('Students')
export class StudentsController {
  constructor(private readonly studentsService: StudentsService) {}

  @Post('/add-new-lead')
  @ApiResponse({
    status: 200,
    description: 'A new Student Lead has been added successfully.',
    type: GetAStudentDto,
  })
  async addNewLead(
    @Body() studentLeadDto: AddStudentLeadDto,
  ): Promise<GetAStudentDto> {
    return await this.studentsService.addNewLead(studentLeadDto);
  }

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new Student created successfully.',
    type: GetAStudentDto,
  })
  @Permissions('create:students')
  async createAStudent(
    @Body() addAStudentDto: AddAStudentDto,
  ): Promise<GetAStudentDto> {
    return await this.studentsService.createAStudent(addAStudentDto);
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all Students (with optional filters)',
    type: PageDto,
  })
  @Permissions('read:students')
  async getAllStudents(
    @Query() query: GetLeadsQueryDto,
    @Query() pageOptions: PageOptionsDto,
    @Req() req?: XRequest,
  ): Promise<PageDto<GetAStudentDto>> {
    return await this.studentsService.getAllStudents(query, pageOptions, req);
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a Student',
    type: GetAStudentDto,
  })
  @Permissions('read:students')
  async getStudentById(@Param('id') id: string): Promise<GetAStudentDto> {
    return await this.studentsService.getStudentById(id);
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Student updated successfully.',
    type: GetAStudentDto,
  })
  @ApiBody({ type: UpdateAStudentDto })
  @Permissions('update:students')
  async updateAStudent(
    @Param('id') id: string,
    @Body() updateStudentDto: UpdateAStudentDto,
  ): Promise<GetAStudentDto> {
    return await this.studentsService.updateAStudent(id, updateStudentDto);
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'Student deleted successfully.',
    type: GetAStudentDto,
  })
  @Permissions('delete:students')
  async deleteAStudent(@Param('id') id: string): Promise<GetAStudentDto> {
    return await this.studentsService.deleteAStudent(id);
  }

  @Get('user/:id')
  @ApiResponse({
    status: 200,
    description: 'Student fetched successfully.',
    type: GetAStudentDto,
  })
  async getAStudentByUserId(@Param('id') id: string): Promise<GetAStudentDto> {
    return await this.studentsService.getAstudentByUserId(id);
  }
}
