import { ApiProperty } from '@nestjs/swagger';
import {
  DegreeLevel,
  IeltsType,
  LeadSource,
  StudentStatus,
  StudentSubStatus,
} from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AddQualificationDto } from './add-qualification.dto';
import { AddVisaDto } from './add-visa.dto';

export class AddStudentLeadDto {
  @ApiProperty({
    description: 'Date when the lead was recorded',
    required: false,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  leadDate?: Date;

  @ApiProperty({
    description: 'Student first name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'Student last name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'Date of birth of the Student',
    required: true,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateOfBirth: Date;

  @ApiProperty({
    description: "Student's work experience",
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  workExperience?: number;

  @ApiProperty({
    description: 'Student CNIC',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  CNIC?: string;

  @ApiProperty({
    description: 'Student cell number',
    required: true,
    type: String,
  })
  @IsString()
  @IsOptional()
  cellNo: string;

  @ApiProperty({
    description: 'Student phone number',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  phoneNo: string;

  @ApiProperty({
    description: 'Student email id',
    required: true,
    type: String,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Student address',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Student nationality',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  nationality?: string;

  @ApiProperty({
    description: 'Does the student have a passport?',
    required: true,
    type: Boolean,
  })
  @IsNotEmpty()
  @IsBoolean()
  passport: boolean;

  @ApiProperty({
    description: 'Student residence city',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({
    description: 'Branch/Office from where the student is being added',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  officeId?: string;

  @ApiProperty({
    description: 'Student IELTS score',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  ieltsScore?: number;

  @ApiProperty({
    description: 'Year student took IELTS',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsDateString()
  ieltsYear?: Date;

  @ApiProperty({
    description: 'Type of IELTS taken by the student',
    required: false,
    enum: IeltsType,
    type: 'string',
  })
  @IsEnum(IeltsType)
  @IsOptional()
  ieltsType?: IeltsType;

  @ApiProperty({
    description: 'Student lead source',
    required: true,
    enum: LeadSource,
    type: 'string',
  })
  @IsEnum(LeadSource)
  @IsNotEmpty()
  leadSource: LeadSource;

  @ApiProperty({
    description: 'Student source name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  leadSourceName: string;

  @ApiProperty({
    description: 'Student process status',
    required: true,
    enum: StudentStatus,
    type: 'string',
  })
  @IsEnum(StudentStatus)
  @IsNotEmpty()
  status: StudentStatus;

  @ApiProperty({
    description: 'Student process sub-status',
    required: true,
    enum: StudentSubStatus,
    type: 'string',
  })
  @IsEnum(StudentSubStatus)
  @IsNotEmpty()
  subStatus: StudentSubStatus;

  @ApiProperty({
    description: 'Last institute attended by Student',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastInstituteAttended: string;

  @ApiProperty({
    description: 'Highest qualification by Student',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastInstituteDegree: string;

  @ApiProperty({
    description: 'Degree program, Student is interested in',
    required: true,
    enum: DegreeLevel,
    type: 'string',
  })
  @IsEnum(DegreeLevel)
  @IsNotEmpty()
  interestedDegreeLevel: DegreeLevel;

  @ApiProperty({
    description: 'Study fields, Student is interested in',
    required: true,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  interestedFields: string[];

  @ApiProperty({
    description: 'Countries, Student is interested in',
    required: true,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  interestedCountries: string[];

  @ApiProperty({
    description: 'All of the Student Qualifications',
    required: true,
    type: [AddQualificationDto],
  })
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => AddQualificationDto)
  qualifications: AddQualificationDto[];

  @ApiProperty({
    description: 'All of the Visas applied by the Student',
    required: false,
    type: [AddVisaDto],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AddVisaDto)
  visas?: AddVisaDto[];

  @ApiProperty({
    description: 'Student referral code',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  referralCode?: string;

  @ApiProperty({
    description: 'Any additional notes about the Student',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
