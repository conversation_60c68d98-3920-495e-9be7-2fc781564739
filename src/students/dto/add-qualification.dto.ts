import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class AddQualificationDto {
  @ApiProperty({
    description: 'Title of the Qualification',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  qualification: string;

  @ApiProperty({
    description: 'Specialized field of the Qualification',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  specialization: string;

  @ApiProperty({
    description: 'The year when the Student completed this Qualification',
    required: true,
    type: String,
    format: 'date',
  })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  passingYear: Date;

  @ApiProperty({
    description: 'Total marks obtained by the Student',
    required: true,
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  obtainedMarks: number;

  @ApiProperty({
    description: 'Total marks of the Qualification',
    required: true,
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  totalMarks: number;
}
