import { ApiProperty } from '@nestjs/swagger';
import { VisaOutcome, VisaType } from '@prisma/client';
import { Type } from 'class-transformer';

import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class AddVisaDto {
  @ApiProperty({
    description: 'The type of the Visa applied by the Student',
    required: true,
    enum: VisaType,
  })
  @IsNotEmpty()
  @IsEnum(VisaType)
  type: VisaType;

  @ApiProperty({
    description: 'The embassy response for the Visa applied',
    required: true,
    enum: VisaOutcome,
  })
  @IsNotEmpty()
  @IsEnum(VisaOutcome)
  outcome: VisaOutcome;

  @ApiProperty({
    description: 'Country to which the Student applied for Visa',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  country: string;

  @ApiProperty({
    description: 'The year when the Student got Visa refusal',
    required: false,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  yearOfRefusal?: Date;
}
