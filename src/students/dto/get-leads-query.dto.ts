import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { DegreeLevel, StudentStatus, StudentSubStatus } from '@prisma/client';

export class GetLeadsQueryDto {
  @ApiPropertyOptional({
    description: 'Search by student first or last name',
    type: String,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Search by interested fields (e.g. Business, Engineering)',
    type: String,
  })
  @IsOptional()
  @IsString()
  interestedFields?: string;

  @ApiPropertyOptional({
    description: 'Filter by degree level of interest',
    enum: DegreeLevel,
  })
  @IsOptional()
  @IsEnum(DegreeLevel)
  degreeLevel?: DegreeLevel;

  @ApiPropertyOptional({
    description: 'Filter by assigned counsellor ID',
    type: String,
  })
  @IsOptional()
  @IsUUID()
  counsellorId?: string;

  @ApiPropertyOptional({
    description: 'Filter by student status',
    enum: StudentStatus,
  })
  @IsOptional()
  @IsEnum(StudentStatus)
  status?: StudentStatus;

  @ApiPropertyOptional({
    description: 'Filter by student substatus',
    enum: StudentSubStatus,
  })
  @IsOptional()
  @IsEnum(StudentSubStatus)
  substatus?: StudentSubStatus;

  @ApiPropertyOptional({
    description: 'Start of date range (createdAt)',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiPropertyOptional({
    description: 'End of date range (createdAt)',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  toDate?: string;
}
