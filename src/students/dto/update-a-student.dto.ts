// src/students/dto/update-student.dto.ts
import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  DegreeLevel,
  IeltsType,
  LeadSource,
  StudentStatus,
  StudentSubStatus,
} from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsDateString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class UpdateAStudentDto {
  @ApiPropertyOptional({
    description: 'Student first name',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: 'Student last name',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    description: 'Date of birth of the Student',
    required: true,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateOfBirth?: Date;

  @ApiPropertyOptional({
    description: "Student's work experience",
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  workExperience?: number;

  @ApiPropertyOptional({
    description: 'Student phone number',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Student residence city',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: 'Student IELTS score',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  ieltsScore?: number;

  @ApiPropertyOptional({
    description: 'Year student took IELTS',
    required: false,
    type: Date,
  })
  @IsOptional()
  @IsDateString()
  ieltsYear?: Date;

  @ApiPropertyOptional({
    description: 'Type of IELTS taken by the student',
    required: false,
    enum: IeltsType,
  })
  @IsEnum(IeltsType)
  @IsOptional()
  ieltsType?: IeltsType;

  @ApiPropertyOptional({
    description: 'Student residence city',
    required: false,
    type: LeadSource,
    enum: LeadSource,
  })
  @IsOptional()
  @IsEnum(LeadSource)
  leadSource?: LeadSource;

  @ApiPropertyOptional({
    description: 'Student source name',
    required: true,
    type: String,
  })
  @IsOptional()
  leadSourceName: string;

  @ApiPropertyOptional({
    description: 'Student process status',
    required: true,
    type: StudentStatus,
    enum: StudentStatus,
    enumName: 'StudentStatus',
  })
  @IsOptional()
  @IsEnum(StudentStatus)
  @IsString()
  status?: StudentStatus;

  @ApiPropertyOptional({
    description: 'Student process sub-status',
    required: true,
    type: StudentSubStatus,
    enum: StudentSubStatus,
    enumName: 'StudentSubStatus',
  })
  @IsOptional()
  @IsString()
  @IsEnum(StudentSubStatus)
  subStatus: StudentSubStatus;

  @ApiPropertyOptional({
    description: 'Any additional notes about the Student',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Last institute attended by Student',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  lastInstituteAttended?: string;

  @ApiPropertyOptional({
    description: 'Highest qualification by Student',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  lastInstituteDegree?: string;

  @ApiPropertyOptional({
    description: 'Study fields, Student is interested in',
    required: false,
    type: String,
    enum: DegreeLevel,
  })
  @IsOptional()
  @IsString()
  interestedDegreeLevel?: DegreeLevel;

  @ApiPropertyOptional({
    description: 'Study fields, Student is interested in',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  interestedFields?: string[];

  @ApiPropertyOptional({
    description: 'Countries, Student is interested in',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  interestedCountries?: string[];

  @ApiPropertyOptional({
    description: 'Student counsellor',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  counsellorId?: string;
}
