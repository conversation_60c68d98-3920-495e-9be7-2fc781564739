import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { AddStudentLeadDto } from './add-student-lead.dto'; // adjust the import path as needed

export class AddAStudentDto extends AddStudentLeadDto {
  @ApiProperty({
    description: 'Student password',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
