import { ApiProperty } from '@nestjs/swagger';
import {
  DegreeLevel,
  IeltsType,
  LeadSource,
  StudentStatus,
  StudentSubStatus,
} from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CounsellorDto } from './counsellor.dto';
import { QualificationDto } from './qualification.dto';
import { UserDto } from './user.dto';
import { VisaDto } from './visa.dto';

export class GetAStudentDto {
  @ApiProperty({
    description: 'Student id',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Date when the lead was recorded',
    required: false,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  leadDate?: Date;

  @ApiProperty({
    description: 'Student first name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'Student last name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'Date of birth of the Student',
    required: true,
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateOfBirth: Date;

  @ApiProperty({
    description: "Student's work experience",
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  workExperience?: number;

  @ApiProperty({
    description: 'Student CNIC',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  CNIC?: string;

  @ApiProperty({
    description: 'Student cell number',
    required: true,
    type: String,
  })
  @IsString()
  @IsOptional()
  cellNo: string;

  @ApiProperty({
    description: 'Student phone number',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  phoneNo?: string;

  @ApiProperty({
    description: 'Student address',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Student nationality',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  nationality?: string;

  @ApiProperty({
    description: 'Does the student have a passport?',
    required: true,
    type: Boolean,
  })
  @IsNotEmpty()
  @IsBoolean()
  passport: boolean;

  @ApiProperty({
    description: 'Student residence city',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({
    description: 'Student IELTS score',
    required: false,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  ieltsScore?: number;

  @ApiProperty({
    description: 'Year student took IELTS',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) =>
    value ? new Date(value).toISOString().split('T')[0] : value,
  )
  ieltsYear?: Date;

  @ApiProperty({
    description: 'Type of IELTS taken by the student',
    required: false,
    enum: IeltsType,
  })
  @IsEnum(IeltsType)
  @IsOptional()
  ieltsType?: IeltsType;

  @ApiProperty({
    description: 'Qualifications of the student',
    required: false,
    type: [QualificationDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QualificationDto)
  qualifications?: QualificationDto[];

  @ApiProperty({
    description: 'Counsellor of the Student',
    required: false,
    type: [VisaDto],
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => VisaDto)
  visas: VisaDto[];

  @ApiProperty({
    description: 'Source of the Student Lead',
    required: true,
    type: LeadSource,
    enum: [LeadSource],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(LeadSource)
  leadSource: LeadSource;

  @ApiProperty({
    description: 'Student source name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  leadSourceName: string;

  @ApiProperty({
    description: 'Student process status',
    required: true,
    type: StudentStatus,
    enum: StudentStatus,
    enumName: 'StudentStatus',
  })
  @IsString()
  @IsEnum(StudentStatus)
  @IsNotEmpty()
  status: StudentStatus;

  @ApiProperty({
    description: 'Student process sub-status',
    required: true,
    type: StudentSubStatus,
    enum: StudentSubStatus,
    enumName: 'StudentSubStatus',
  })
  @IsString()
  @IsEnum(StudentSubStatus)
  @IsNotEmpty()
  subStatus: StudentSubStatus;

  @ApiProperty({
    description: 'Last institute attended by Student',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastInstituteAttended: string;

  @ApiProperty({
    description: 'Highest qualification by Student',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  lastInstituteDegree: string;

  @ApiProperty({
    description: 'Degree programs, Student is interested in',
    required: true,
    type: String,
    enum: DegreeLevel,
  })
  @IsEnum(DegreeLevel)
  @IsNotEmpty()
  interestedDegreeLevel: DegreeLevel;

  @ApiProperty({
    description: 'Study fields, Student is interested in',
    required: true,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  interestedFields: string[];

  @ApiProperty({
    description: 'Countries, Student is interested in',
    required: true,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  interestedCountries: string[];

  @ApiProperty({
    description: 'Student referral code',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  referralCode?: string;

  @ApiProperty({
    description: 'Any additional notes about the Student',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'The date when the Student was created',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the Student was updated',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'User object of Student',
    required: true,
    type: UserDto,
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UserDto)
  user: UserDto;

  @ApiProperty({
    description: 'Counsellor of the Student',
    required: false,
    type: CounsellorDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CounsellorDto)
  counsellor?: CounsellorDto;
}
