import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import { XRequest } from 'src/Auth/XRequest';
import { PrismaService } from 'src/prisma/prisma.service';
import { AddAStudentDto } from './dto/add-a-student.dto';
import { AddStudentLeadDto } from './dto/add-student-lead.dto';
import { GetAStudentDto } from './dto/get-a-student.dto';
import { UpdateAStudentDto } from './dto/update-a-student.dto';
import { GetLeadsQueryDto } from './dto/get-leads-query.dto';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { PageMetaDto } from '../common/dtos/pageMeta.dto';
import { PageDto } from '../common/dtos/page.dto';

TODO: 'Validate for all if the user is DELETED or not before performing any operation';
@Injectable()
export class StudentsService {
  constructor(private prisma: PrismaService) {}

  private async validateUserNotDeleted(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { status: true },
    });

    if (!user || user.status === 'DELETED') {
      throw new HttpException(
        'User is deleted or not found',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /*
    No need to check for the field values because it will already be checked
    in our DTOs. Checks like, value should not be empty or optional and contain
    only a certain datatypes, will be validated in our DTOs. Hence reducing our
    code
  */

  async addNewLead(studentLeadDto: AddStudentLeadDto): Promise<GetAStudentDto> {
    try {
      const {
        email,
        firstName,
        lastName,
        workExperience,
        dateOfBirth,
        cellNo,
        passport,
        officeId,
        CNIC,
        address,
        ieltsScore,
        ieltsType,
        ieltsYear,
        leadDate,
        nationality,
        notes,
        phoneNo,
        city,
        leadSource,
        status,
        lastInstituteAttended,
        lastInstituteDegree,
        interestedDegreeLevel,
        interestedFields,
        interestedCountries,
        referralCode,
        qualifications,
        visas,
      } = studentLeadDto;

      // Check if the email already exists in the User table
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      // Generate random password
      const generatedPassword = Math.random().toString(36).slice(-8);

      const hashedPassword = await bcrypt.hash(generatedPassword, 10);

      const student = await this.prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            name: `${firstName} ${lastName}`,
            email,
            password: hashedPassword,
            status: 'INACTIVE',
            roles: { connect: { name: 'STUDENT' } },
          },
        });

        const student = await prisma.student.create({
          data: {
            userId: user.id,
            firstName,
            lastName,
            workExperience,
            dateOfBirth,
            cellNo,
            officeId,
            passport,
            status,
            CNIC,
            address,
            ieltsScore,
            ieltsType,
            ieltsYear: new Date(ieltsYear),
            leadDate,
            nationality,
            notes,
            phoneNo,
            city,
            leadSource,
            lastInstituteAttended,
            lastInstituteDegree,
            interestedDegreeLevel,
            interestedFields,
            interestedCountries,
            referralCode,
          } as any,
        });

        const processedQualifications = (qualifications || []).map((q) => ({
          qualification: q.qualification,
          specialization: q.specialization,
          passingYear: q.passingYear,
          obtainedMarks: q.obtainedMarks,
          totalMarks: q.totalMarks,
          studentId: student.id,
        }));

        if (processedQualifications.length > 0) {
          await prisma.qualification.createMany({
            data: processedQualifications,
          });
        }

        const processedVisas = (visas || []).map((v) => ({
          type: v.type,
          outcome: v.outcome,
          country: v.country,
          yearOfRefusal: v.yearOfRefusal,
          studentId: student.id,
        }));

        if (processedVisas.length > 0) {
          await prisma.visa.createMany({
            data: processedVisas,
          });
        }

        return await prisma.student.findUnique({
          where: { id: student.id },
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
              },
            },
            firstName: true,
            lastName: true,
            workExperience: true,
            dateOfBirth: true,
            CNIC: true,
            phoneNo: true,
            status: true,
            cellNo: true,
            address: true,
            nationality: true,
            passport: true,
            city: true,
            ieltsScore: true,
            ieltsYear: true,
            ieltsType: true,
            leadSource: true,
            leadSourceName: true,
            subStatus: true,
            lastInstituteAttended: true,
            lastInstituteDegree: true,
            interestedDegreeLevel: true,
            interestedFields: true,
            interestedCountries: true,
            referralCode: true,
            notes: true,
            visas: true,
            qualifications: {
              select: {
                id: true,
                qualification: true,
                specialization: true,
                passingYear: true,
                obtainedMarks: true,
                totalMarks: true,
              },
            },
            counsellor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    status: true,
                  },
                },
              },
            },
            createdAt: true,
            updatedAt: true,
          },
        });
      });

      return student;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Email already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createAStudent(
    addAStudentDto: AddAStudentDto,
  ): Promise<GetAStudentDto> {
    try {
      const {
        email,
        firstName,
        lastName,
        workExperience,
        dateOfBirth,
        password,
        cellNo,
        passport,
        CNIC,
        address,
        ieltsScore,
        ieltsType,
        ieltsYear,
        leadDate,
        nationality,
        notes,
        phoneNo,
        city,
        leadSource,
        status,
        lastInstituteAttended,
        lastInstituteDegree,
        interestedDegreeLevel,
        interestedFields,
        interestedCountries,
        referralCode,
        qualifications,
        visas,
      } = addAStudentDto;

      // Check if the email already exists in the User table
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      const student = await this.prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            email,
            name: `${firstName} ${lastName}`,
            password: hashedPassword,
            status: 'ACTIVE',
          },
        });

        await this.validateUserNotDeleted(user.id);

        const student = await prisma.student.create({
          data: {
            userId: user.id,
            firstName,
            lastName,
            workExperience,
            dateOfBirth,
            cellNo,
            passport,
            status,
            CNIC,
            address,
            ieltsScore,
            ieltsType,
            ieltsYear,
            leadDate,
            nationality,
            notes,
            phoneNo,
            city,
            leadSource,
            lastInstituteAttended,
            lastInstituteDegree,
            interestedDegreeLevel,
            interestedFields,
            interestedCountries,
            referralCode,
          },
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
              },
            },
            firstName: true,
            lastName: true,
            workExperience: true,
            dateOfBirth: true,
            CNIC: true,
            phoneNo: true,
            cellNo: true,
            address: true,
            nationality: true,
            passport: true,
            city: true,
            status: true,
            ieltsScore: true,
            ieltsYear: true,
            ieltsType: true,
            leadSource: true,
            lastInstituteAttended: true,
            lastInstituteDegree: true,
            interestedDegreeLevel: true,
            interestedFields: true,
            interestedCountries: true,
            referralCode: true,
            notes: true,
            visas: true,
            qualifications: {
              select: {
                id: true,
                qualification: true,
                specialization: true,
                passingYear: true,
                obtainedMarks: true,
                totalMarks: true,
              },
            },
            counsellor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    status: true,
                  },
                },
              },
            },
            createdAt: true,
            updatedAt: true,
          },
        });

        const processedQualifications = (qualifications || []).map((q) => ({
          qualification: q.qualification,
          specialization: q.specialization,
          passingYear: q.passingYear,
          obtainedMarks: q.obtainedMarks,
          totalMarks: q.totalMarks,
          studentId: student.id,
        }));

        if (processedQualifications.length > 0) {
          await prisma.qualification.createMany({
            data: processedQualifications,
          });
        }

        const processedVisas = (visas || []).map((v) => ({
          type: v.type,
          outcome: v.outcome,
          country: v.country,
          yearOfRefusal: v.yearOfRefusal,
          studentId: student.id,
        }));

        if (processedVisas.length > 0) {
          await prisma.visa.createMany({
            data: processedVisas,
          });
        }

        return await prisma.student.findUnique({
          where: { id: student.id },
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
              },
            },
            firstName: true,
            lastName: true,
            workExperience: true,
            dateOfBirth: true,
            CNIC: true,
            phoneNo: true,
            status: true,
            cellNo: true,
            address: true,
            nationality: true,
            passport: true,
            city: true,
            ieltsScore: true,
            ieltsYear: true,
            ieltsType: true,
            leadSource: true,
            leadSourceName: true,
            subStatus: true,
            lastInstituteAttended: true,
            lastInstituteDegree: true,
            interestedDegreeLevel: true,
            interestedFields: true,
            interestedCountries: true,
            referralCode: true,
            notes: true,
            visas: true,
            qualifications: {
              select: {
                id: true,
                qualification: true,
                specialization: true,
                passingYear: true,
                obtainedMarks: true,
                totalMarks: true,
              },
            },
            counsellor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    status: true,
                  },
                },
              },
            },
            createdAt: true,
            updatedAt: true,
          },
        });
      });

      return student;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Email already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllStudents(
    query: GetLeadsQueryDto,
    pageOptions: PageOptionsDto,
    req?: XRequest,
  ): Promise<PageDto<GetAStudentDto>> {
    try {
      const {
        name,
        interestedFields,
        degreeLevel,
        counsellorId,
        status,
        substatus,
        fromDate,
        toDate,
      } = query;
      const { skip = 0, take = 10 } = pageOptions;
      const baseFilters: any = {};
      if (name) {
        baseFilters.OR = [
          { firstName: { contains: name, mode: 'insensitive' } },
          { lastName: { contains: name, mode: 'insensitive' } },
          {
            user: {
              name: { contains: name, mode: 'insensitive' },
            },
          },
        ];
      }

      if (interestedFields) {
        const fieldsArray = Array.isArray(interestedFields)
          ? interestedFields
          : String(interestedFields)
              .split(',')
              .map((field) => field.trim());

        if (fieldsArray.length > 0) {
          baseFilters.applications = {
            some: {
              program: {
                name: {
                  in: fieldsArray,
                  mode: 'insensitive',
                },
              },
            },
          };
        }
      }

      if (degreeLevel) {
        baseFilters.interestedDegreeLevel = degreeLevel;
      }

      if (status) {
        baseFilters.status = status;
      }

      if (substatus) {
        baseFilters.subStatus = substatus;
      }

      if (fromDate || toDate) {
        baseFilters.createdAt = {};
        if (fromDate) baseFilters.createdAt.gte = new Date(fromDate);
        if (toDate) baseFilters.createdAt.lte = new Date(toDate);
      }
      let students: any[] = [];
      let itemCount = 0;
      if (req?.user?.roles.includes('ADMIN')) {
        if (counsellorId) {
          baseFilters.counsellorId = counsellorId;
        }
        [students, itemCount] = await Promise.all([
          this.prisma.student.findMany({
            where: baseFilters,
            select: this.getStudentSelect(),
            skip,
            take,
          }),
          this.prisma.student.count({ where: baseFilters }),
        ]);
      } else if (req?.user?.roles.includes('COUNSELLOR')) {
        const counsellor = await this.prisma.employee.findUnique({
          where: { userId: req.user.id },
          select: { id: true },
        });
        if (!counsellor) {
          throw new NotFoundException('Counsellor not found');
        }
        baseFilters.counsellorId = counsellor.id;
        [students, itemCount] = await Promise.all([
          this.prisma.student.findMany({
            where: baseFilters,
            select: this.getStudentSelect(),
            skip,
            take,
          }),
          this.prisma.student.count({ where: baseFilters }),
        ]);
      } else {
        throw new Error('User role not authorized to access students.');
      }
      const meta = new PageMetaDto({ itemCount, pageOptionsDto: pageOptions });
      return new PageDto<GetAStudentDto>(students, meta);
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Helper method to clean up duplication
  private getStudentSelect() {
    return {
      id: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
        },
      },
      firstName: true,
      lastName: true,
      workExperience: true,
      dateOfBirth: true,
      CNIC: true,
      phoneNo: true,
      cellNo: true,
      address: true,
      nationality: true,
      passport: true,
      city: true,
      ieltsScore: true,
      ieltsYear: true,
      ieltsType: true,
      leadSource: true,
      status: true,
      leadSourceName: true,
      subStatus: true,
      lastInstituteAttended: true,
      lastInstituteDegree: true,
      interestedDegreeLevel: true,
      interestedFields: true,
      interestedCountries: true,
      referralCode: true,
      notes: true,
      visas: true,
      qualifications: {
        select: {
          id: true,
          qualification: true,
          specialization: true,
          passingYear: true,
          obtainedMarks: true,
          totalMarks: true,
        },
      },
      counsellorId: true,
      counsellor: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
            },
          },
        },
      },
      createdAt: true,
      updatedAt: true,
    };
  }

  async getStudentById(id: string): Promise<GetAStudentDto> {
    try {
      const student = await this.prisma.student.findUnique({
        where: { id },
        include: {
          user: true,
          visas: true,
          qualifications: {
            select: {
              id: true,
              qualification: true,
              specialization: true,
              passingYear: true,
              obtainedMarks: true,
              totalMarks: true,
            },
          },
          counsellor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  status: true,
                },
              },
            },
          },
        },
      });

      if (!student) {
        throw new HttpException(
          `Student with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      await this.validateUserNotDeleted(student.user.id);

      return student;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateAStudent(
    id: string,
    updateStudentDto: UpdateAStudentDto,
  ): Promise<GetAStudentDto> {
    try {
      const existingStudent = await this.prisma.student.findUnique({
        where: { id },
        include: { user: true },
      });

      if (!existingStudent) {
        throw new HttpException(
          `Student with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      await this.validateUserNotDeleted(existingStudent.user.id);

      const response = await this.prisma.student.update({
        where: { id },
        data: updateStudentDto,
        select: {
          id: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
            },
          },
          firstName: true,
          lastName: true,
          workExperience: true,
          dateOfBirth: true,
          CNIC: true,
          phoneNo: true,
          cellNo: true,
          address: true,
          nationality: true,
          passport: true,
          city: true,
          ieltsScore: true,
          ieltsYear: true,
          ieltsType: true,
          leadSource: true,
          status: true,
          leadSourceName: true,
          subStatus: true,
          lastInstituteAttended: true,
          lastInstituteDegree: true,
          interestedDegreeLevel: true,
          interestedFields: true,
          interestedCountries: true,
          referralCode: true,
          notes: true,
          visas: true,
          qualifications: {
            select: {
              id: true,
              qualification: true,
              specialization: true,
              passingYear: true,
              obtainedMarks: true,
              totalMarks: true,
            },
          },
          counsellorId: true,
          counsellor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  status: true,
                },
              },
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      });

      return response;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteAStudent(id: string): Promise<GetAStudentDto> {
    try {
      const student = await this.prisma.student.findUnique({
        where: { id },
        include: {
          user: true,
          visas: true,
          qualifications: {
            select: {
              id: true,
              qualification: true,
              specialization: true,
              passingYear: true,
              obtainedMarks: true,
              totalMarks: true,
            },
          },
          counsellor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  status: true,
                },
              },
            },
          },
        },
      });

      if (!student) {
        throw new HttpException(
          `Student with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      await this.validateUserNotDeleted(student.user.id);

      // Instead of deleting, we'll mark the user as DELETED
      await this.prisma.user.update({
        where: { id: student.user.id },
        data: { status: 'DELETED' },
      });

      return student;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async getAstudentByUserId(id: string): Promise<GetAStudentDto> {
    try {
      const student = await this.prisma.student.findUnique({
        where: { userId: id },
        select: {
          id: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
            },
          },
          firstName: true,
          lastName: true,
          workExperience: true,
          dateOfBirth: true,
          CNIC: true,
          phoneNo: true,
          cellNo: true,
          address: true,
          nationality: true,
          passport: true,
          city: true,
          ieltsScore: true,
          ieltsYear: true,
          ieltsType: true,
          leadSource: true,
          status: true,
          leadSourceName: true,
          subStatus: true,
          lastInstituteAttended: true,
          lastInstituteDegree: true,
          interestedDegreeLevel: true,
          interestedFields: true,
          interestedCountries: true,
          referralCode: true,
          notes: true,
          visas: true,
          qualifications: {
            select: {
              id: true,
              qualification: true,
              specialization: true,
              passingYear: true,
              obtainedMarks: true,
              totalMarks: true,
            },
          },
          counsellor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  status: true,
                },
              },
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      });

      return student;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
