import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as process from 'process';
import { AppModule } from './app.module';
import { PrismaService } from './prisma/prisma.service';

const PORT: string | number = process.env.BACKEND_PORT || 5000;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // Prisma
  const prismaService = app.get(PrismaService);
  await prismaService.enableShutdownHooks(app);
  // Swagger
  const config = new DocumentBuilder()
    .setTitle('FES CRM API')
    .setDescription('FES CRM Portal API')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Cors
  app.enableCors({
    origin: '*',
  });

  // use class validators
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  await app.listen(PORT);
}

bootstrap();
