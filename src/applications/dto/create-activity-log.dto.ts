import { ApiProperty } from '@nestjs/swagger';
import { ActivityLogType } from '@prisma/client';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateActivityLogDto {
  @ApiProperty({ enum: ActivityLogType })
  @IsEnum(ActivityLogType)
  type: ActivityLogType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  metadata?: string;
}
