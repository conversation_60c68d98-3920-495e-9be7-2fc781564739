import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationStatus, DegreeLevel } from '@prisma/client';
import { ApplicationsController } from './applications.controller';
import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { GetApplicationDto } from './dto/get-application.dto';
import { UpdateApplicationDto } from './dto/update.application.dto';

describe('ApplicationsController', () => {
  let controller: ApplicationsController;
  let service: ApplicationsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicationsController],
      providers: [
        {
          provide: ApplicationsService,
          useValue: {
            createApplication: jest.fn(),
            getApplications: jest.fn(),
            getApplicationsByStudentId: jest.fn(),
            getApplicationById: jest.fn(),
            updateApplication: jest.fn(),
            deleteApplication: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ApplicationsController>(ApplicationsController);
    service = module.get<ApplicationsService>(ApplicationsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createApplication', () => {
    it('should create an application successfully', async () => {
      const createApplicationDto: CreateApplicationDto = {
        studentId: 'student-1',
        universityId: 'university-1',
        programId: 'program-1',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Bachelor',
        interestedDegreeLevel: DegreeLevel.MASTERS,
        status: ApplicationStatus.APPLIED,
        appliedDate: new Date(),
        decisionDate: new Date(),
        visaAppliedDate: new Date(),
        visaDecisionDate: new Date(),
        notes: '',
      };
      const result = {
        id: '1',
        studentId: 'student-1',
        universityId: 'university-1',
        programId: 'program-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        status: ApplicationStatus.ACCEPTED,
        appliedDate: new Date(),
        decisionDate: new Date(),
        visaAppliedDate: new Date(),
        visaDecisionDate: new Date(),
        notes: '',
      };
      jest.spyOn(service, 'createApplication').mockResolvedValue(result);

      expect(await controller.createApplication(createApplicationDto)).toBe(
        result,
      );
    });
  });

  describe('getAllApplications', () => {
    it('should get all applications successfully', async () => {
      const result: GetApplicationDto[] = [
        {
          id: '1',
          studentId: 'student-1',
          universityId: 'university-1',
          programId: 'program-1',
          status: ApplicationStatus.APPLIED,
          appliedDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      const mockQuery = {};
      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      const mockResult = {
        data: result,
        meta: { page: 1, take: 10, itemCount: 1, pageCount: 1, hasPreviousPage: false, hasNextPage: false }
      };
      jest.spyOn(service, 'getApplications').mockResolvedValue(mockResult);

      expect(await controller.getAllApplications(mockQuery, mockPageOptions)).toBe(mockResult);
    });

    it('should return an empty array if no applications exist', async () => {
      const mockQuery = {};
      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      const mockEmptyResult = {
        data: [],
        meta: { page: 1, take: 10, itemCount: 0, pageCount: 0, hasPreviousPage: false, hasNextPage: false }
      };
      jest.spyOn(service, 'getApplications').mockResolvedValue(mockEmptyResult);

      expect(await controller.getAllApplications(mockQuery, mockPageOptions)).toEqual(mockEmptyResult);
    });
  });

  describe('getApplicationsByStudentId', () => {
    it('should get applications by student ID successfully', async () => {
      const studentId = 'student-1';
      const result: GetApplicationDto[] = [
        {
          id: '1',
          studentId: studentId,
          universityId: 'university-1',
          programId: 'program-1',
          status: ApplicationStatus.APPLIED,
          appliedDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      jest
        .spyOn(service, 'getApplicationsByStudentId')
        .mockResolvedValue(result);

      expect(await controller.getApplicationsByStudentId(studentId)).toBe(
        result,
      );
    });

    it('should return an empty array if no applications exist for the student ID', async () => {
      const studentId = 'student-1';
      jest.spyOn(service, 'getApplicationsByStudentId').mockResolvedValue([]);

      expect(await controller.getApplicationsByStudentId(studentId)).toEqual(
        [],
      );
    });

    it('should return an error if the service throws an exception', async () => {
      const studentId = 'student-1';
      const error = new HttpException(
        'Error message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      jest
        .spyOn(service, 'getApplicationsByStudentId')
        .mockRejectedValue(error);

      await expect(
        controller.getApplicationsByStudentId(studentId),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getApplicationById', () => {
    it('should get an application by ID successfully', async () => {
      const result: GetApplicationDto = {
        id: '1',
        studentId: 'student-1',
        universityId: 'university-1',
        programId: 'program-1',
        status: ApplicationStatus.APPLIED,
        appliedDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(service, 'getApplicationById').mockResolvedValue(result);

      expect(await controller.getApplicationById('1')).toBe(result);
    });

    it('should throw NotFoundException if application not found', async () => {
      jest
        .spyOn(service, 'getApplicationById')
        .mockRejectedValue(new NotFoundException('Application not found'));

      await expect(controller.getApplicationById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateApplication', () => {
    it('should update an application successfully', async () => {
      const updateApplicationDto: UpdateApplicationDto = {
        status: ApplicationStatus.ACCEPTED,
        decisionDate: new Date(),
      };
      const result = {
        id: '1',
        studentId: 'student-1',
        universityId: 'university-1',
        programId: 'program-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        status: ApplicationStatus.ACCEPTED,
        appliedDate: new Date(),
        decisionDate: new Date(),
        visaAppliedDate: new Date(),
        visaDecisionDate: new Date(),
        notes: '',
      };
      jest.spyOn(service, 'updateApplication').mockResolvedValue(result);

      expect(
        await controller.updateApplication('1', updateApplicationDto),
      ).toBe(result);
    });

    it('should throw NotFoundException if application not found', async () => {
      const updateApplicationDto: UpdateApplicationDto = {
        status: ApplicationStatus.ACCEPTED,
      };
      jest
        .spyOn(service, 'updateApplication')
        .mockRejectedValue(new NotFoundException('Application not found'));

      await expect(
        controller.updateApplication('999', updateApplicationDto),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteApplication', () => {
    it('should delete an application successfully', async () => {
      jest.spyOn(service, 'deleteApplication').mockResolvedValue(undefined);

      await expect(controller.deleteApplication('1')).resolves.toBeUndefined();
    });

    it('should throw NotFoundException if application not found', async () => {
      jest
        .spyOn(service, 'deleteApplication')
        .mockRejectedValue(new NotFoundException('Application not found'));

      await expect(controller.deleteApplication('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
