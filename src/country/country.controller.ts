import { GetCountryDto } from './dto/get-country.dto';
import { CountryService } from './country.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { UpdateCountryDto } from './dto/update-country.dto';
import { CreateCountryDto } from './dto/create-country.dto';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiBearerAuth()
@ApiTags('countries')
@Controller('countries')
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @Post()
  @ApiResponse({
    status: 201,
    description: 'Country created successfully',
    type: GetCountryDto,
  })
  create(@Body() createCountryDto: CreateCountryDto) {
    return this.countryService.create(createCountryDto);
  }

  @Get()
  @ApiResponse({ status: 200, type: [GetCountryDto] })
  findAll() {
    return this.countryService.findAll();
  }

  @Get(':id')
  @ApiResponse({ status: 200, type: GetCountryDto })
  findOne(@Param('id') id: string) {
    return this.countryService.findOne(id);
  }

  @Patch(':id')
  @ApiResponse({ status: 200, type: GetCountryDto })
  update(@Param('id') id: string, @Body() updateCountryDto: UpdateCountryDto) {
    return this.countryService.update(id, updateCountryDto);
  }

  @Delete(':id')
  @ApiResponse({ status: 200, type: GetCountryDto })
  remove(@Param('id') id: string) {
    return this.countryService.remove(id);
  }
}
