import { ApiProperty } from '@nestjs/swagger';
import { Region, Office, Employee } from '@prisma/client';
import { OfficeDTO } from 'src/cities/dto/office.dto';
import { EmployeeDTO } from 'src/offices/dto/employee.dto';
import { GetRegionDto } from 'src/regions/dto/get-a-region.dto';

export class GetCountryDto {
  @ApiProperty({ example: 'uuid-value' })
  id: string;

  @ApiProperty({ example: 'Germany' })
  name: string;

  @ApiProperty({
    type: () => [GetRegionDto],
    description: 'List of regions',
    required: false,
  })
  regions?: Region[];

  @ApiProperty({
    type: () => [OfficeDTO],
    description: 'List of offices',
    required: false,
  })
  offices?: Office[];

  @ApiProperty({
    type: () => [EmployeeDTO],
    description: 'List of employees',
    required: false,
  })
  employees?: Employee[];
}
