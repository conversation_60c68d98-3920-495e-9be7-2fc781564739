import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { CitiesService } from './cities.service';
import { CityWithOfficesDTO } from './dto/city-with-offices.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { GetACityDto } from './dto/get-a-city.dto';
import { UpdateCityDto } from './dto/update-a-city.dto';

@ApiBearerAuth()
@Controller('cities')
@ApiTags('Cities')
export class CitiesController {
  constructor(private readonly CitiesService: CitiesService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new City created successfully.',
    type: GetACityDto,
  })
  @Permissions('create:cities')
  async createACity(
    @Body() createCityDto: CreateCityDto,
  ): Promise<GetACityDto> {
    try {
      return await this.CitiesService.createACity(createCityDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all Cities.',
    type: [GetACityDto],
  })
  @Permissions('READ')
  async getAllCities(): Promise<GetACityDto[]> {
    try {
      return await this.CitiesService.getAllCities();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a City.',
    type: GetACityDto,
  })
  @Permissions('READ')
  async getCityById(@Param('id') id: string): Promise<GetACityDto> {
    try {
      return await this.CitiesService.getCityById(id);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id/offices')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a City.',
    type: CityWithOfficesDTO,
  })
  @Permissions('READ')
  async getOfficesByCityId(
    @Param('id') id: string,
  ): Promise<CityWithOfficesDTO> {
    try {
      return await this.CitiesService.getOfficesByCityId(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'City updated successfully.',
    type: UpdateCityDto,
  })
  @Permissions('UPDATE')
  async updateACity(
    @Param('id') id: string,
    @Body() updateCityDto: UpdateCityDto,
  ): Promise<GetACityDto> {
    try {
      return await this.CitiesService.updateACity(id, updateCityDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'City deleted successfully.',
    type: GetACityDto,
  })
  @Permissions('DELETE')
  async deleteACity(@Param('id') id: string): Promise<GetACityDto> {
    try {
      return await this.CitiesService.deleteACity(id);
    } catch (error) {
      throw error;
    }
  }
}
