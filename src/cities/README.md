# Cities Module

The Cities module is a core part of the application’s backend API, built using NestJS. It provides endpoints to create, retrieve, update, and delete cities as well as retrieve the offices associated with a city. All functionalities are documented using Swagger decorators and are covered by unit tests written with Jest.

## Features

- **Create a City:** Add a new city by providing its name and associated region ID.
- **Retrieve Cities:** Fetch all cities or a specific city by its ID.
- **Retrieve City Offices:** Get a city along with its associated offices.
- **Update a City:** Update the details (name, region ID) of an existing city.
- **Delete a City:** Remove a city from the system.

## Routes

### `POST /cities`

- **Description:** Creates a new city.
- **Request Body:**
  - `name` (string): The name of the city.
  - `regionId` (string): The region identifier.
- **Response:** Returns the created city object.
- **Decorators:**
  - `@Permissions('WRITE')`
  - `@ApiRolesPermissions(['ADMIN'], ['WRITE'])`

### `GET /cities`

- **Description:** Retrieves all cities.
- **Response:** An array of city objects.
- **Decorators:**
  - `@Permissions('READ')`
  - `@ApiRolesPermissions(['ADMIN'], ['READ'])`

### `GET /cities/:id`

- **Description:** Retrieves a single city by its ID.
- **URL Parameter:**
  - `id` (string): The city's ID.
- **Response:** The city object.
- **Decorators:**
  - `@Permissions('READ')`
  - `@ApiRolesPermissions(['ADMIN'], ['READ'])`

### `GET /cities/:id/offices`

- **Description:** Retrieves a city along with its offices.
- **URL Parameter:**
  - `id` (string): The city's ID.
- **Response:** A city object including an array of offices.
- **Decorators:**
  - `@Permissions('READ')`
  - `@ApiRolesPermissions(['ADMIN'], ['READ'])`

### `PATCH /cities/:id`

- **Description:** Updates a city.
- **URL Parameter:**
  - `id` (string): The city's ID.
- **Request Body:**
  - `name` (string): The new city name.
  - `regionId` (string): The updated region identifier.
- **Response:** The updated city object.
- **Decorators:**
  - `@Permissions('UPDATE')`
  - `@ApiRolesPermissions(['ADMIN'], ['UPDATE'])`

### `DELETE /cities/:id`

- **Description:** Deletes a city.
- **URL Parameter:**
  - `id` (string): The city's ID.
- **Response:** The deleted city object.
- **Decorators:**
  - `@Permissions('DELETE')`
  - `@ApiRolesPermissions(['ADMIN'], ['DELETE'])`

## Directory Structure

The module is organized under the `src/cities` directory:

src/ └─ cities/ ├─ cities.controller.ts // Controller handling HTTP routes ├─ cities.service.ts // Business logic and Prisma interactions ├─ dto/ │ ├─ create-city.dto.ts │ ├─ get-a-city.dto.ts │ ├─ update-a-city.dto.ts │ └─ city-with-offices.dto.ts ├─ cities.controller.spec.ts // Controller unit tests └─ cities.service.spec.ts // Service unit tests (with both success & error cases)

---

## Unit Tests Documentation (Service Layer)

The service tests are designed to cover both the happy paths and error scenarios for each method. Below is an outline of the test cases implemented in `cities.service.spec.ts`:

### 1. `createACity(createCityDto: CreateCityDto): Promise<GetACityDto>`

- **Success Case:**
  - _Test Name:_ "should create a city successfully"
  - _Behavior:_
    - Mocks `prisma.city.create` to resolve with a sample city object.
    - Asserts that the returned value matches the expected city and that the method was called with the correct DTO.
- **Failure Case:**
  - _Test Name:_ "should throw HttpException if prisma.city.create fails"
  - _Behavior:_
    - Mocks `prisma.city.create` to reject with an error (containing a message and status).
    - Expects the method to throw an `HttpException` with the error message.

### 2. `getAllCities(): Promise<GetACityDto[]>`

- **Success Case:**
  - _Test Name:_ "should return an array of cities"
  - _Behavior:_
    - Mocks `prisma.city.findMany` to resolve with an array of city objects.
    - Verifies that the returned array matches the expected data.
- **Failure Case:**
  - _Test Name:_ "should throw HttpException if prisma.city.findMany fails"
  - _Behavior:_
    - Mocks `prisma.city.findMany` to reject with an error.
    - Asserts that the error thrown is an `HttpException` with the appropriate message.

### 3. `getCityById(id: string): Promise<GetACityDto>`

- **Success Case:**
  - _Test Name:_ "should return a city when found"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to resolve with a valid city object.
    - Asserts that the correct city is returned.
- **City Not Found Case:**
  - _Test Name:_ "should throw HttpException with NOT_FOUND if city is not found"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to resolve with `null`.
    - Expects an `HttpException` with a message like "City with id [id] not found".
- **General Error Case:**
  - _Test Name:_ "should throw HttpException if prisma.city.findUnique fails"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to reject with an error.
    - Asserts that the error is rethrown as an `HttpException`.

### 4. `getOfficesByCityId(id: string): Promise<CityWithOfficesDTO>`

- **Success Case:**
  - _Test Name:_ "should return a city with its offices when the city exists"
  - _Behavior:_
    - Mocks the first call to `prisma.city.findUnique` to return a valid city.
    - Mocks the second call to `prisma.city.findUnique` to resolve with a detailed object (including an `offices` array).
    - Asserts that the returned object contains the expected office data.
- **City Not Found Case:**
  - _Test Name:_ "should throw HttpException with NOT_FOUND if the city does not exist"
  - _Behavior:_
    - Mocks the first call to `prisma.city.findUnique` to return `null`.
    - Expects an `HttpException` with a "not found" message.
- **Second Call Failure Case:**
  - _Test Name:_ "should throw HttpException if the second prisma.city.findUnique fails"
  - _Behavior:_
    - Mocks the first call to return a valid city and the second call to reject with an error.
    - Asserts that an `HttpException` is thrown with the correct error message.

### 5. `updateACity(id: string, updateCityDto: UpdateCityDto): Promise<GetACityDto>`

- **Success Case:**
  - _Test Name:_ "should update a city when it exists"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return an existing city.
    - Mocks `prisma.city.update` to resolve with the updated city object.
    - Verifies that the returned city matches the updated details.
- **City Not Found Case:**
  - _Test Name:_ "should throw HttpException with NOT_FOUND if city does not exist"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return `null`.
    - Expects an `HttpException` with a message like "City with id [id] not found".
- **Update Failure Case:**
  - _Test Name:_ "should throw HttpException if prisma.city.update fails"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return an existing city, then mocks `prisma.city.update` to reject with an error.
    - Asserts that an `HttpException` is thrown with the appropriate error message.

### 6. `deleteACity(id: string): Promise<GetACityDto>`

- **Success Case:**
  - _Test Name:_ "should delete a city when it exists"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return a valid city.
    - Mocks `prisma.city.delete` to resolve with the deleted city object.
    - Verifies that the returned object matches the expected deleted city.
- **City Not Found Case:**
  - _Test Name:_ "should throw HttpException with NOT_FOUND if city does not exist"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return `null`.
    - Expects an `HttpException` with a "not found" message.
- **Delete Failure Case:**
  - _Test Name:_ "should throw HttpException if prisma.city.delete fails"
  - _Behavior:_
    - Mocks `prisma.city.findUnique` to return an existing city, then mocks `prisma.city.delete` to reject with an error.
    - Asserts that an `HttpException` is thrown with the appropriate error message.

---

## Running the Tests

The tests are executed using Jest. To run the test suite for the Cities module, run:

```bash
npm run test src/cities/cities.service.spec.ts
```
