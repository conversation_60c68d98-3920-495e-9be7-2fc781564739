import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from 'src/prisma/prisma.service';
import { CitiesService } from './cities.service';

describe('CitiesService', () => {
  let service: CitiesService;
  let prisma: PrismaService;

  // Create a mock PrismaService with jest.fn() for each method on the city model.
  const mockPrisma = {
    city: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CitiesService,
        {
          provide: PrismaService,
          useValue: mockPrisma,
        },
      ],
    }).compile();

    service = module.get<CitiesService>(CitiesService);
    prisma = module.get<PrismaService>(PrismaService);
    jest.clearAllMocks();
  });

  describe('createACity', () => {
    it('should create a city successfully', async () => {
      // Temporary DTO data for createCityDto
      const createCityDto = { name: 'TestCity', regionId: 'region1' };
      const expectedCity = {
        id: '1',
        name: createCityDto.name,
        regionId: createCityDto.regionId,
      };

      mockPrisma.city.create.mockResolvedValue(expectedCity);

      const result = await service.createACity(createCityDto);
      expect(prisma.city.create).toHaveBeenCalledWith({
        data: { name: createCityDto.name, regionId: createCityDto.regionId },
      });
      expect(result).toEqual(expectedCity);
    });

    it('should throw HttpException if prisma.city.create fails', async () => {
      const createCityDto = { name: 'TestCity', regionId: 'region1' };
      const mockError = {
        message: 'Error creating city',
        status: HttpStatus.BAD_REQUEST,
      };

      mockPrisma.city.create.mockRejectedValue(mockError);

      try {
        await service.createACity(createCityDto);
        // Fail the test if no error was thrown:
        fail('createACity did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });

  describe('getAllCities', () => {
    it('should return an array of cities', async () => {
      const expectedCities = [
        { id: '1', name: 'CityOne', regionId: 'region1' },
        { id: '2', name: 'CityTwo', regionId: 'region2' },
      ];

      mockPrisma.city.findMany.mockResolvedValue(expectedCities);

      const result = await service.getAllCities();

      expect(prisma.city.findMany).toHaveBeenCalled();
      expect(result).toEqual(expectedCities);
    });

    it('should throw HttpException if prisma.city.findMany fails', async () => {
      const mockError = {
        message: 'Error fetching cities',
        status: HttpStatus.BAD_REQUEST,
      };

      mockPrisma.city.findMany.mockRejectedValue(mockError);

      try {
        await service.getAllCities();
        // Fail the test if no error was thrown:
        fail('getAllCities did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });

  describe('getCityById', () => {
    it('should return a city when found', async () => {
      const cityId = '1';
      const expectedCity = { id: cityId, name: 'CityOne', regionId: 'region1' };

      mockPrisma.city.findUnique.mockResolvedValue(expectedCity);

      const result = await service.getCityById(cityId);
      expect(prisma.city.findUnique).toHaveBeenCalledWith({
        where: { id: cityId },
      });
      expect(result).toEqual(expectedCity);
    });

    it('should throw HttpException with NOT_FOUND if city is not found', async () => {
      const cityId = 'nonexistent';
      mockPrisma.city.findUnique.mockResolvedValue(null);

      try {
        await service.getCityById(cityId);
        // Fail the test if no error was thrown:
        fail('getCityById did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(`City with id ${cityId} not found`);
      }
    });

    it('should throw HttpException if prisma.city.findUnique fails', async () => {
      const cityId = '1';
      const mockError = {
        message: 'Error fetching city',
        status: HttpStatus.BAD_REQUEST,
      };
      mockPrisma.city.findUnique.mockRejectedValue(mockError);

      try {
        await service.getCityById(cityId);
        // Fail the test if no error was thrown:
        fail('getCityById did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });

  describe('getOfficesByCityId', () => {
    it('should return a city with its offices when the city exists', async () => {
      const cityId = '1';
      // First call: check if city exists
      mockPrisma.city.findUnique.mockResolvedValueOnce({
        id: cityId,
        name: 'CityOne',
        regionId: 'region1',
      });
      // Second call: return the city with offices. Note that the provided service hardcodes the id in the second call.
      const expectedOfficesResponse = {
        id: cityId,
        name: 'CityOne',
        regionId: 'region1',
        offices: [
          {
            id: 'office1',
            name: 'OfficeOne',
            cityId: cityId,
            employees: [{ id: 'emp1' }],
          },
        ],
      };
      mockPrisma.city.findUnique.mockResolvedValueOnce(expectedOfficesResponse);

      const result = await service.getOfficesByCityId(cityId);
      expect(prisma.city.findUnique).toHaveBeenCalledTimes(2);
      expect(result).toEqual(expectedOfficesResponse);
    });

    it('should throw HttpException with NOT_FOUND if the city does not exist', async () => {
      const cityId = 'nonexistent';
      // First call returns null
      mockPrisma.city.findUnique.mockResolvedValueOnce(null);

      try {
        await service.getOfficesByCityId(cityId);
        // Fail the test if no error was thrown:
        fail('getOfficesByCityId did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(`City with id ${cityId} not found`);
      }
    });

    it('should throw HttpException if the second prisma.city.findUnique fails', async () => {
      const cityId = '1';
      // First call simulates an existing city.
      mockPrisma.city.findUnique.mockResolvedValueOnce({
        id: cityId,
        name: 'CityOne',
        regionId: 'region1',
      });
      // Second call throws an error.
      const mockError = {
        message: 'Error fetching offices',
        status: HttpStatus.BAD_REQUEST,
      };
      mockPrisma.city.findUnique.mockRejectedValueOnce(mockError);

      try {
        await service.getOfficesByCityId(cityId);
        // Fail the test if no error was thrown:
        fail('getOfficesByCityId did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });

  describe('updateACity', () => {
    it('should update a city when it exists', async () => {
      const cityId = '1';
      // Temporary update DTO data
      const updateCityDto = { name: 'UpdatedCity', regionId: 'regionUpdated' };
      const existingCity = { id: cityId, name: 'OldCity', regionId: 'region1' };
      const expectedUpdatedCity = { id: cityId, ...updateCityDto };

      mockPrisma.city.findUnique.mockResolvedValueOnce(existingCity);
      mockPrisma.city.update.mockResolvedValueOnce(expectedUpdatedCity);

      const result = await service.updateACity(cityId, updateCityDto);
      expect(prisma.city.findUnique).toHaveBeenCalledWith({
        where: { id: cityId },
      });
      expect(prisma.city.update).toHaveBeenCalledWith({
        where: { id: cityId },
        data: { name: updateCityDto.name, regionId: updateCityDto.regionId },
      });
      expect(result).toEqual(expectedUpdatedCity);
    });

    it('should throw HttpException with NOT_FOUND if city does not exist', async () => {
      const cityId = 'nonexistent';
      const updateCityDto = { name: 'UpdatedCity', regionId: 'regionUpdated' };

      mockPrisma.city.findUnique.mockResolvedValueOnce(null);

      try {
        await service.updateACity(cityId, updateCityDto);
        // Fail the test if no error was thrown:
        fail('updateACity did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(`City with id ${cityId} not found`);
      }
    });

    it('should throw HttpException if prisma.city.update fails', async () => {
      const cityId = '1';
      const updateCityDto = { name: 'UpdatedCity', regionId: 'regionUpdated' };
      const existingCity = { id: cityId, name: 'OldCity', regionId: 'region1' };

      mockPrisma.city.findUnique.mockResolvedValueOnce(existingCity);
      const mockError = {
        message: 'Error updating city',
        status: HttpStatus.BAD_REQUEST,
      };
      mockPrisma.city.update.mockRejectedValueOnce(mockError);

      try {
        await service.updateACity(cityId, updateCityDto);
        // Fail the test if no error was thrown:
        fail('updateACity did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });

  describe('deleteACity', () => {
    it('should delete a city when it exists', async () => {
      const cityId = '1';
      const existingCity = { id: cityId, name: 'CityOne', regionId: 'region1' };
      const expectedDeletedCity = {
        id: cityId,
        name: 'CityOne',
        regionId: 'region1',
      };

      mockPrisma.city.findUnique.mockResolvedValueOnce(existingCity);
      mockPrisma.city.delete.mockResolvedValueOnce(expectedDeletedCity);

      const result = await service.deleteACity(cityId);
      expect(prisma.city.findUnique).toHaveBeenCalledWith({
        where: { id: cityId },
      });
      expect(prisma.city.delete).toHaveBeenCalledWith({
        where: { id: cityId },
      });
      expect(result).toEqual(expectedDeletedCity);
    });

    it('should throw HttpException with NOT_FOUND if city does not exist', async () => {
      const cityId = 'nonexistent';

      mockPrisma.city.findUnique.mockResolvedValueOnce(null);

      try {
        await service.deleteACity(cityId);
        // Fail the test if no error was thrown:
        fail('deleteACity did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(`City with id ${cityId} not found`);
      }
    });

    it('should throw HttpException if prisma.city.delete fails', async () => {
      const cityId = '1';
      const existingCity = { id: cityId, name: 'CityOne', regionId: 'region1' };

      mockPrisma.city.findUnique.mockResolvedValueOnce(existingCity);
      const mockError = {
        message: 'Error deleting city',
        status: HttpStatus.BAD_REQUEST,
      };
      mockPrisma.city.delete.mockRejectedValueOnce(mockError);

      try {
        await service.deleteACity(cityId);
        // Fail the test if no error was thrown:
        fail('deleteACity did not throw an error');
      } catch (error) {
        // Verify both check httpexception (statusCode) and error message
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toContain(mockError.message);
      }
    });
  });
});
