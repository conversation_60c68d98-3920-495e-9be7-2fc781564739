import { Test, TestingModule } from '@nestjs/testing';
import { CitiesController } from './cities.controller';
import { CitiesService } from './cities.service';

// Create a partial mock for CitiesService with jest.fn() for each method.
const mockCitiesService = {
  createACity: jest.fn(),
  getAllCities: jest.fn(),
  getCityById: jest.fn(),
  getOfficesByCityId: jest.fn(),
  updateACity: jest.fn(),
  deleteACity: jest.fn(),
};

describe('CitiesController', () => {
  let controller: CitiesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CitiesController],
      providers: [
        {
          provide: CitiesService,
          useValue: mockCitiesService,
        },
      ],
    }).compile();

    controller = module.get<CitiesController>(CitiesController);
    jest.clearAllMocks();
  });

  describe('createACity', () => {
    it('should create a new city', async () => {
      const createCityDto = { name: 'City1', regionId: 'region1' }; // dummy DTO data
      const expectedCity = { id: '1', name: 'City1', regionId: 'region1' };

      // Set the mock to resolve with the expected city.
      mockCitiesService.createACity.mockResolvedValue(expectedCity);

      const result = await controller.createACity(createCityDto);
      expect(result).toEqual(expectedCity);
      expect(mockCitiesService.createACity).toHaveBeenCalledWith(createCityDto);
    });
  });

  describe('getAllCities', () => {
    it('should return an array of cities', async () => {
      const expectedCities = [
        { id: '1', name: 'City1', regionId: 'region1' },
        { id: '2', name: 'City2', regionId: 'region2' },
      ];

      mockCitiesService.getAllCities.mockResolvedValue(expectedCities);

      const result = await controller.getAllCities();
      expect(result).toEqual(expectedCities);
      expect(mockCitiesService.getAllCities).toHaveBeenCalled();
    });
  });

  describe('getCityById', () => {
    it('should return a city for a given id', async () => {
      const cityId = '1';
      const expectedCity = { id: cityId, name: 'City1', regionId: 'region1' };

      mockCitiesService.getCityById.mockResolvedValue(expectedCity);

      const result = await controller.getCityById(cityId);
      expect(result).toEqual(expectedCity);
      expect(mockCitiesService.getCityById).toHaveBeenCalledWith(cityId);
    });
  });

  describe('getOfficesByCityId', () => {
    it('should return a city with its offices for a given id', async () => {
      const cityId = '1';
      const expectedResponse = {
        id: cityId,
        name: 'City1',
        regionId: 'region1',
        offices: [{ id: 'office1', name: 'Office1', cityId }],
      };

      mockCitiesService.getOfficesByCityId.mockResolvedValue(expectedResponse);

      const result = await controller.getOfficesByCityId(cityId);
      expect(result).toEqual(expectedResponse);
      expect(mockCitiesService.getOfficesByCityId).toHaveBeenCalledWith(cityId);
    });
  });

  describe('updateACity', () => {
    it('should update a city', async () => {
      const cityId = '1';
      const updateCityDto = { name: 'UpdatedCity', regionId: 'regionUpdated' };
      const expectedCity = {
        id: cityId,
        name: 'UpdatedCity',
        regionId: 'regionUpdated',
      };

      mockCitiesService.updateACity.mockResolvedValue(expectedCity);

      const result = await controller.updateACity(cityId, updateCityDto);
      expect(result).toEqual(expectedCity);
      expect(mockCitiesService.updateACity).toHaveBeenCalledWith(
        cityId,
        updateCityDto,
      );
    });
  });

  describe('deleteACity', () => {
    it('should delete a city', async () => {
      const cityId = '1';
      const expectedCity = { id: cityId, name: 'City1', regionId: 'region1' };

      mockCitiesService.deleteACity.mockResolvedValue(expectedCity);

      const result = await controller.deleteACity(cityId);
      expect(result).toEqual(expectedCity);
      expect(mockCitiesService.deleteACity).toHaveBeenCalledWith(cityId);
    });
  });
});
