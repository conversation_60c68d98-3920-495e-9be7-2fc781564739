import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateCityDto {
  @ApiProperty({
    description: 'City Name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Region id of the City',
    required: false,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  regionId: string;
}
