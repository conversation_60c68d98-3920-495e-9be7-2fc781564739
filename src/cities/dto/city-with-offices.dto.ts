import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, ValidateNested } from 'class-validator';
import { OfficeDTO } from './office.dto';

export class CityWithOfficesDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the City',
    required: true,
  })
  id: string;

  @ApiProperty({
    example: 'string',
    description: 'Name of the City',
    required: false,
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'string',
    description: 'Region id the City belongs to',
    required: false,
  })
  regionId: string;

  @ApiProperty({
    type: [OfficeDTO],
    description: 'List of Offices in the City',
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => OfficeDTO)
  offices?: OfficeDTO[];
}
