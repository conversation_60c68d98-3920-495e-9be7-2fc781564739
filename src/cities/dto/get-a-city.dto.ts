import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GetRegionDto } from 'src/regions/dto/get-a-region.dto';

export class GetACityDto {
  @ApiProperty({
    description: 'City id',
    required: false,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'City name',
    required: false,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Region id of the City',
    required: false,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  regionId: string;

  @ApiProperty({ type: () => GetRegionDto, required: false })
  @IsOptional()
  region?: GetRegionDto;
}
