import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateCityDto {
  @ApiProperty({
    description: 'City name',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Region id of the City',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  regionId?: string;
}
