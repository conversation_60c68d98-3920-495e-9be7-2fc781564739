import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';

export class OfficeDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the Office',
    required: false,
  })
  id: string;

  @ApiProperty({ example: 'string', description: 'Name of the Office' })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'string',
    description: 'City id where the Office is located',
  })
  cityId: string;

  @ApiProperty({
    type: [String],
    description: 'List of Employee ids associated with the Office',
    example: ['string', 'string'],
    required: false,
  })
  @IsArray()
  employeeId?: string[];
}
