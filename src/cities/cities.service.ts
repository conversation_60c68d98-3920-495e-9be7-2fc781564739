import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

import { PrismaService } from 'src/prisma/prisma.service';
import { CityWithOfficesDTO } from './dto/city-with-offices.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { GetACityDto } from './dto/get-a-city.dto';
import { UpdateCityDto } from './dto/update-a-city.dto';

@Injectable()
export class CitiesService {
  constructor(private prisma: PrismaService) {}

  /* 
    No need to check for the field values because it will already be checked
    in our DTOs. Checks like, value should not be empty or optional and contain
    only a certain datatypes, will be validated in our DTOs. Hence reducing our
    code
  */

  async createACity(createCityDto: CreateCityDto): Promise<GetACityDto> {
    try {
      const response = await this.prisma.city.create({
        data: {
          name: createCityDto.name,
          regionId: createCityDto.regionId,
        },
      });

      return response;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllCities(): Promise<GetACityDto[]> {
    try {
      const response = await this.prisma.city.findMany({
        include: { region: true },
      });
      return response;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getCityById(id: string): Promise<GetACityDto> {
    try {
      const city = await this.prisma.city.findUnique({
        where: { id },
      });

      if (!city) {
        throw new HttpException(
          `City with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return city;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOfficesByCityId(id: string): Promise<CityWithOfficesDTO> {
    try {
      const city = await this.prisma.city.findUnique({
        where: { id },
      });

      if (!city) {
        throw new HttpException(
          `City with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      const offices = await this.prisma.city.findUnique({
        where: { id },
        // This select statement will only return what it is asked for
        select: {
          id: true,
          name: true,
          regionId: true,
          offices: {
            select: {
              id: true,
              name: true,
              cityId: true,
              employees: {
                select: { id: true },
              },
            },
          },
        },
      });

      return offices;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateACity(
    id: string,
    updateCityDto: UpdateCityDto,
  ): Promise<GetACityDto> {
    try {
      const existingcity = await this.prisma.city.findUnique({
        where: { id },
      });

      if (!existingcity) {
        throw new HttpException(
          `City with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      const response = await this.prisma.city.update({
        where: { id },
        data: {
          name: updateCityDto.name,
          regionId: updateCityDto.regionId,
        },
      });

      return response;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteACity(id: string): Promise<GetACityDto> {
    try {
      const city = await this.prisma.city.findUnique({
        where: { id },
      });

      if (!city) {
        throw new HttpException(
          `City with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      const response = await this.prisma.city.delete({
        where: { id },
      });

      return response;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
