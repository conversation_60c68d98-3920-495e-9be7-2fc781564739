import { ConflictException, HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'src/prisma/prisma.service';
import { EmployeesService } from './employees.service';

describe('EmployeesService', () => {
  let service: EmployeesService;
  let prisma: jest.Mocked<PrismaService>;

  const prismaMock = {
    employee: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    role: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeesService,
        { provide: PrismaService, useValue: prismaMock },
      ],
    }).compile();

    service = module.get<EmployeesService>(EmployeesService);
    prisma = module.get<PrismaService>(
      PrismaService,
    ) as jest.Mocked<PrismaService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAnEmployee', () => {
    it('should successfully create an employee', async () => {
      const dto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '**********',
        password: 'password123',
      };

      const mockUser = {
        id: 'user-1',
        email: dto.email,
        name: `${dto.firstName} ${dto.lastName}`,
        password: 'hashedPassword',
        status: 'ACTIVE',
      };

      const mockEmployee = {
        id: 'employee-1',
        firstName: dto.firstName,
        lastName: dto.lastName,
        phone: dto.phone,
        officeId: null,
        regionId: null,
        students: [],
        user: {
          id: mockUser.id,
          name: mockUser.name,
          email: mockUser.email,
          status: mockUser.status,
          roles: [],
        },
      };

      // Mock that email doesn't exist
      prismaMock.user.findUnique.mockResolvedValue(null);

      // Mock role exists
      prismaMock.role.findUnique.mockResolvedValue({
        id: 'role-1',
        name: 'EMPLOYEE',
        description: 'Employee role',
      });

      // Mock transaction
      prismaMock.$transaction.mockImplementation(async (callback) => {
        return callback(prismaMock);
      });

      // Mock user and employee creation
      prismaMock.user.create.mockResolvedValue(mockUser);
      prismaMock.employee.create.mockResolvedValue(mockEmployee);

      const result = await service.createAnEmployee(dto);

      expect(result).toEqual(mockEmployee);
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { email: dto.email },
      });
      expect(prismaMock.$transaction).toHaveBeenCalled();
    });

    it('should throw ConflictException if email already exists', async () => {
      const dto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '**********',
        password: 'password123',
      };

      // Mock that email already exists
      prismaMock.user.findUnique.mockResolvedValue({
        id: 'existing-user',
        email: dto.email,
        name: 'Existing User',
        password: 'hashedPassword',
        status: 'ACTIVE',
      });

      await expect(service.createAnEmployee(dto)).rejects.toThrow(ConflictException);

      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { email: dto.email },
      });
    });

    it('should throw HttpException on other errors', async () => {
      const dto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '**********',
        password: 'password123',
      };

      // Mock that email doesn't exist
      prismaMock.user.findUnique.mockResolvedValue(null);

      // Mock role exists
      prismaMock.role.findUnique.mockResolvedValue({
        id: 'role-1',
        name: 'EMPLOYEE',
        description: 'Employee role',
      });

      // Mock transaction to throw error
      prismaMock.$transaction.mockRejectedValue(new Error('Database error'));

      await expect(service.createAnEmployee(dto)).rejects.toThrow(HttpException);
    });
  });

  describe('getAllEmployees', () => {
    it('should return a list of employees', async () => {
      const employees = [
        {
          id: '1',
          user: { id: '1', name: 'John Doe', email: '<EMAIL>' },
        },
      ];
      prismaMock.employee.findMany.mockResolvedValue(employees);

      await expect(service.getAllEmployees()).resolves.toEqual(employees);
    });

    it('should throw HttpException on failure', async () => {
      prismaMock.employee.findMany.mockRejectedValue(
        new Error('Database error'),
      );
      await expect(service.getAllEmployees()).rejects.toThrow(HttpException);
    });
  });

  describe('getEmployeeById', () => {
    it('should return an employee by ID', async () => {
      const employee = { id: '1', user: { id: '1', name: 'John Doe' } };
      prismaMock.employee.findUnique.mockResolvedValue(employee);

      await expect(service.getEmployeeById('1')).resolves.toEqual(employee);
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(service.getEmployeeById('1')).rejects.toThrow(HttpException);
    });
  });

  describe('updateAnEmployee', () => {
    it('should update an employee', async () => {
      const updatedEmployee = {
        id: '1',
        user: { id: '1', name: 'Updated Name' },
      };
      prismaMock.employee.findUnique.mockResolvedValue(updatedEmployee);
      prismaMock.employee.update.mockResolvedValue(updatedEmployee);

      await expect(
        service.updateAnEmployee('1', { firstName: 'Updated', lastName: 'Name', phone: '0987654321' }),
      ).resolves.toEqual(updatedEmployee);
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(
        service.updateAnEmployee('1', { firstName: 'Updated', lastName: 'Name', phone: '0987654321' }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('deleteAnEmployee', () => {
    it('should delete an employee and return it', async () => {
      const employee = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        phone: '**********',
        officeId: null,
        regionId: null,
        students: [],
        user: {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'ACTIVE',
          roles: [],
        },
      };

      prismaMock.employee.findUnique.mockResolvedValue(employee);
      prismaMock.user.update.mockResolvedValue({
        ...employee.user,
        status: 'DELETED',
      });

      const result = await service.deleteAnEmployee('1');

      expect(result).toEqual(employee);
      expect(prismaMock.employee.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        select: expect.any(Object),
      });
      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: employee.user.id },
        data: { status: 'DELETED' },
      });
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(service.deleteAnEmployee('1')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw HttpException if employee already deleted', async () => {
      const employee = {
        id: '1',
        user: {
          id: '1',
          status: 'DELETED',
        },
      };

      prismaMock.employee.findUnique.mockResolvedValue(employee);
      await expect(service.deleteAnEmployee('1')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
