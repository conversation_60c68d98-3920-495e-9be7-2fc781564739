import { ApiProperty } from '@nestjs/swagger';
import { LeadSource } from '@prisma/client';
import { IsDateString, IsOptional, IsString } from 'class-validator';

export class StudentDto {
  @ApiProperty({
    description: 'The id of the Student',
    required: true,
    type: String,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The User id of the Student',
    required: true,
    type: String,
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'The lead source of the Student',
    required: true,
    type: LeadSource,
    enum: LeadSource,
  })
  @IsString()
  leadSource: LeadSource;

  @ApiProperty({
    description: 'The Agent id of the Student',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  agentId?: string | null;

  @ApiProperty({
    description: 'The Counsellor id of the Student',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  counsellorId?: string;

  @ApiProperty({
    description: 'The creation date of the Student object',
    required: true,
    type: String,
  })
  @IsDateString()
  createdAt: Date;

  @ApiProperty({
    description: 'The update date of the Student object',
    required: true,
    type: String,
  })
  @IsDateString()
  updatedAt: Date;
}
