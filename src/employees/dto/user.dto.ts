import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsArray, IsEmail, IsString, ValidateNested } from 'class-validator';
import { RoleDto } from './role.dto';

export class UserDto {
  @ApiProperty({
    description: 'The id of the User',
    required: true,
    type: String,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The name of the User',
    required: true,
    type: String,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The email of the User',
    required: true,
    type: String,
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'The current status of the User',
    required: true,
    type: UserStatus,
    enum: UserStatus,
  })
  @IsString()
  status: UserStatus;

  @ApiProperty({
    description: 'The Roles associated with the User object',
    required: true,
    type: [RoleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RoleDto)
  roles?: RoleDto[];
}
