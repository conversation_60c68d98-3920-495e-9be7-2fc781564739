import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { StudentDto } from './student.dto';
import { UserDto } from './user.dto';

export class GetAnEmployeeDto {
  @ApiProperty({
    description: 'The id of the Employee',
    required: true,
    type: String,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'First name of the Employee',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({
    description: 'Last name of the Employee',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'Phone no of the Employee',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({
    description: 'The Office id of the Employee',
    required: false,
    type: String,
  })
  @IsString()
  officeId?: string;

  @ApiProperty({
    description: 'The Region id of the Employee',
    required: false,
    type: String,
  })
  @IsString()
  regionId?: string;

  @ApiProperty({
    description:
      'The Students associated with the Employee (Counsellor will have them)',
    required: false,
    type: [StudentDto],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => StudentDto)
  students?: StudentDto[];

  @ApiProperty({
    description: 'The User object of the Employee',
    required: false,
    type: UserDto,
  })
  @ValidateNested()
  @Type(() => UserDto)
  user: UserDto;
}
