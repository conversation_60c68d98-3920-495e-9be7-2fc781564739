import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class RoleDto {
  @ApiProperty({
    description: 'The id of the Role',
    required: true,
    type: String,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The name of the Role',
    required: true,
    type: String,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The description of the Role',
    required: true,
    type: String,
  })
  @IsString()
  description: string;
}
