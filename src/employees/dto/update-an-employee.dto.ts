import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateAnEmployeeDto {
  @ApiProperty({
    description: 'First name of the Employee',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({
    description: 'Last name of the Employee',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: 'Phone no of the Employee',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  phone?: string;
}
