import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { CreateAnEmployeeDto } from './dto/create-an-employee.dto';
import { GetAnEmployeeDto } from './dto/get-an-employee.dto';
import { UpdateAnEmployeeDto } from './dto/update-an-employee.dto';
import { EmployeesService } from './employees.service';

@ApiBearerAuth()
@Controller('employees')
@ApiTags('Employees')
export class EmployeesController {
  constructor(private readonly employeesService: EmployeesService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new Employee created successfully.',
    type: GetAnEmployeeDto,
  })
  @Permissions('create:employees')
  async createAnEmployee(
    @Body() createAnEmployeeDto: CreateAnEmployeeDto,
  ): Promise<GetAnEmployeeDto> {
    try {
      return await this.employeesService.createAnEmployee(createAnEmployeeDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all Employees.',
    type: [GetAnEmployeeDto],
  })
  @Permissions('read:employees')
  async getAllEmployees(): Promise<GetAnEmployeeDto[]> {
    try {
      return await this.employeesService.getAllEmployees();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved an Employee.',
    type: GetAnEmployeeDto,
  })
  @Permissions('read:employees')
  async getEmployeeById(@Param('id') id: string): Promise<GetAnEmployeeDto> {
    try {
      return await this.employeesService.getEmployeeById(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Employee updated successfully.',
    type: UpdateAnEmployeeDto,
  })
  @Permissions('update:employees')
  async updateAnEmployee(
    @Param('id') id: string,
    @Body() updateCityDto: UpdateAnEmployeeDto,
  ): Promise<GetAnEmployeeDto> {
    try {
      return await this.employeesService.updateAnEmployee(id, updateCityDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'Employee deleted successfully.',
    type: GetAnEmployeeDto,
  })
  @Permissions('delete:employees')
  async deleteAnEmployee(@Param('id') id: string): Promise<GetAnEmployeeDto> {
    try {
      return await this.employeesService.deleteAnEmployee(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch('roles/:id')
  @ApiResponse({
    status: 200,
    description: 'Employee updated successfully.',
    type: GetAnEmployeeDto,
  })
  @Permissions('update:employees')
  async updateEmployeeRoles(
    @Param('id') id: string,
    @Body() updatedRoles: string[],
  ): Promise<GetAnEmployeeDto> {
    try {
      return await this.employeesService.updateEmployeeRoels(id, updatedRoles);
    } catch (error) {
      throw error;
    }
  }
}
