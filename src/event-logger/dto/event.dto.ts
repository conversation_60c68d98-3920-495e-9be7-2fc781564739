import { IsArray, IsObject, IsOptional, IsString } from 'class-validator';
import { ActorDto } from './actor.dto';
import { ContextDto } from './context.dto';
import { TargetEntityDto } from './target-entity.dto';

export class EventDto {
  @IsString()
  eventType: string;

  @IsOptional()
  @IsObject()
  beforeSnapshot?: Record<string, any>;

  @IsOptional()
  @IsObject()
  afterSnapshot?: Record<string, any>;

  @IsString()
  sessionId: string;

  @IsString()
  browserUserAgent: string;

  @IsString()
  geolocation: string;

  @IsString()
  type: string;

  @IsString()
  entityId: string;

  @IsArray()
  @IsString({ each: true })
  fieldModified: string[];

  @IsOptional()
  @IsString()
  parentEventId?: string;

  @IsOptional()
  actor: ActorDto;

  @IsOptional()
  targetEntity: TargetEntityDto;

  @IsOptional()
  context?: ContextDto;
}
