import { Test, TestingModule } from '@nestjs/testing';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { PrismaService } from 'src/prisma/prisma.service';
import { EventDto } from './dto/event.dto';
import { EventLoggerService } from './event-logger.service';
import { EventType } from '@prisma/client';

describe('EventLoggerService', () => {
  let service: EventLoggerService;
  let prismaMock: DeepMockProxy<PrismaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventLoggerService,
        { provide: PrismaService, useValue: mockDeep<PrismaService>() },
      ],
    }).compile();

    service = module.get<EventLoggerService>(EventLoggerService);
    prismaMock = module.get(PrismaService);
  });

  it('should log an event successfully', async () => {
    const eventData: EventDto = {
      eventType: 'USER_LOGIN',
      beforeSnapshot: {
        password:
          '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe',
      },
      afterSnapshot: {
        password:
          '$2a$12$u8DN.vv3EQlh9tyHOSJfpOvchRN3EBje6m9J0w2f.B3InVZt.aTl6',
      },
      sessionId: 'session_789',
      browserUserAgent: 'Mozilla/5.0',
      geolocation: 'Lahore, Pakistan',
      type: 'user',
      entityId: 'user_123',
      fieldModified: ['lastLogin'],
      actor: {
        userId: 'user_123',
        role: 'admin',
        ipAddress: '127.0.0.1',
        deviceId: 'device_abc',
      },
      targetEntity: {
        type: 'user',
        entityId: 'user_123',
        fieldModified: ['lastLogin'],
      },
      context: {
        sessionId: 'session_789',
        browserUserAgent: 'Mozilla/5.0',
        geolocation: 'Lahore, Pakistan',
      },
    };

    const mockEvent = {
      id: 'event_uuid',
      eventType: 'USER_LOGIN',
      actorId: 'actor_uuid',
      targetEntityId: 'target_entity_uuid',
      contextId: 'context_uuid',
      timestamp: new Date(),
      beforeSnapshot: eventData.beforeSnapshot,
      afterSnapshot: eventData.afterSnapshot,
      parentEventId: null,
      type: eventData.type,
      entityId: eventData.entityId,
      fieldModified: eventData.fieldModified,
      sessionId: eventData.sessionId,
      browserUserAgent: eventData.browserUserAgent,
      geolocation: eventData.geolocation,
    };



    prismaMock.actor.create.mockResolvedValue({
      id: 'actor_uuid',
      ...eventData.actor,
    });

    prismaMock.targetEntity.create.mockResolvedValue({
      id: 'target_entity_uuid',
      ...eventData.targetEntity,
    });

    prismaMock.context.create.mockResolvedValue({
      id: 'context_uuid',
      ...eventData.context,
    });

    prismaMock.event.create.mockResolvedValue(mockEvent);

    const result = await service.logEvent(eventData);

    expect(result).toEqual(mockEvent);
    expect(prismaMock.actor.create).toHaveBeenCalledWith({
      data: eventData.actor,
    });
    expect(prismaMock.targetEntity.create).toHaveBeenCalledWith({
      data: eventData.targetEntity,
    });
    expect(prismaMock.context.create).toHaveBeenCalledWith({
      data: eventData.context,
    });
    expect(prismaMock.event.create).toHaveBeenCalledWith({
      data: {
        eventType: eventData.eventType,
        type: eventData.targetEntity.type,
        entityId: eventData.targetEntity.entityId,
        fieldModified: eventData.targetEntity.fieldModified,
        actorId: 'actor_uuid',
        contextId: 'context_uuid',
        targetEntityId: 'target_entity_uuid',
        beforeSnapshot: eventData.beforeSnapshot,
        afterSnapshot: eventData.afterSnapshot,
        sessionId: eventData.context.sessionId,
        browserUserAgent: eventData.context.browserUserAgent,
        geolocation: eventData.context.geolocation,
        parentEventId: null,
      },
    });
  });

  it('should handle errors during event logging', async () => {
    const eventData: EventDto = {
      eventType: 'USER_LOGIN',
      beforeSnapshot: {
        password:
          '$2b$10$B316L0RXesCP1bEZgEd7d.I02cwudktXk4b9IdvI7j91O2bBkPVBe',
      },
      afterSnapshot: {
        password:
          '$2a$12$u8DN.vv3EQlh9tyHOSJfpOvchRN3EBje6m9J0w2f.B3InVZt.aTl6',
      },
      sessionId: 'session_789',
      browserUserAgent: 'Mozilla/5.0',
      geolocation: 'Lahore, Pakistan',
      type: 'user',
      entityId: 'user_123',
      fieldModified: ['lastLogin'],
      actor: {
        userId: 'user_123',
        role: 'admin',
        ipAddress: '127.0.0.1',
        deviceId: 'device_abc',
      },
      targetEntity: {
        type: 'user',
        entityId: 'user_123',
        fieldModified: ['lastLogin'],
      },
      context: {
        sessionId: 'session_789',
        browserUserAgent: 'Mozilla/5.0',
        geolocation: 'Lahore, Pakistan',
      },
    };

    prismaMock.actor.create.mockRejectedValue(new Error('Actor creation failed'));
    prismaMock.targetEntity.create.mockRejectedValue(new Error('Target entity creation failed'));
    prismaMock.context.create.mockRejectedValue(new Error('Context creation failed'));

    await expect(service.logEvent(eventData)).rejects.toThrow(
      'Failed to log event',
    );
    expect(prismaMock.actor.create).toHaveBeenCalledWith({
      data: eventData.actor,
    });
  });

  describe('getEvents', () => {
    it('should return paginated events', async () => {
      const mockEvents = [
        {
          id: '1',
          type: 'STUDENT',
          eventType: EventType.CREATE_STUDENT,
          entityId: 'student-123',
          fieldModified: ['name'],
          timestamp: new Date(),
          actorId: 'actor-1',
          targetEntityId: 'target-1',
          contextId: 'context-1',
          sessionId: 'session-1',
          browserUserAgent: 'Chrome',
          geolocation: '0,0',
          beforeSnapshot: null,
          afterSnapshot: null,
          parentEventId: null,
        },
        {
          id: '2',
          type: 'STUDENT',
          eventType: EventType.UPDATE_STUDENT,
          entityId: 'student-123',
          fieldModified: ['email'],
          timestamp: new Date(),
          actorId: 'actor-2',
          targetEntityId: 'target-2',
          contextId: 'context-2',
          sessionId: 'session-2',
          browserUserAgent: 'Firefox',
          geolocation: '0,0',
          beforeSnapshot: null,
          afterSnapshot: null,
          parentEventId: null,
        },
      ];

      prismaMock.event.count.mockResolvedValue(2);
      prismaMock.event.findMany.mockResolvedValue(mockEvents);

      const result = await service.getEvents({
        page: 1,
        limit: 10,
        sort: 'desc',
      });

      expect(result.data).toEqual(mockEvents);
      expect(result.meta).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });
    });

    it('should apply filters correctly', async () => {
      const queryParams = {
        page: 1,
        limit: 10,
        sort: 'desc' as const,
        type: EventType.CREATE_STUDENT,
        entityType: 'STUDENT',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      };

      await service.getEvents(queryParams);

      expect(prismaMock.event.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            AND: [
              { eventType: EventType.CREATE_STUDENT },
              { type: 'STUDENT' },
              { timestamp: { gte: expect.any(Date) } },
              { timestamp: { lte: expect.any(Date) } },
            ],
          },
        }),
      );
    });
  });

  describe('getStudentEvents', () => {
    it('should return paginated student events', async () => {
      const studentId = 'student-123';
      const mockEvents = [
        {
          id: '1',
          type: 'STUDENT',
          eventType: EventType.CREATE_STUDENT,
          entityId: studentId,
          fieldModified: ['name'],
          timestamp: new Date(),
          actorId: 'actor-1',
          targetEntityId: 'target-1',
          contextId: 'context-1',
          sessionId: 'session-1',
          browserUserAgent: 'Chrome',
          geolocation: '0,0',
          beforeSnapshot: null,
          afterSnapshot: null,
          parentEventId: null,
        },
        {
          id: '2',
          type: 'STUDENT',
          eventType: EventType.UPDATE_STUDENT,
          entityId: studentId,
          fieldModified: ['email'],
          timestamp: new Date(),
          actorId: 'actor-2',
          targetEntityId: 'target-2',
          contextId: 'context-2',
          sessionId: 'session-2',
          browserUserAgent: 'Firefox',
          geolocation: '0,0',
          beforeSnapshot: null,
          afterSnapshot: null,
          parentEventId: null,
        },
      ];

      // Mock the student check
      prismaMock.student.findUnique.mockResolvedValue({
        id: studentId,
        userId: 'user-123',
        firstName: 'Test',
        lastName: 'Student',
        CNIC: '12345-6789012-3',
        address: '123 Test St',
        city: 'Test City',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '1234567890',
        phoneNo: '0987654321',
        leadDate: new Date(),
        nationality: 'Pakistani',
        passport: true,
        workExperience: 2,
        ieltsScore: 7.5,
        ieltsYear: new Date('2023-01-01'),
        ieltsType: 'NON_UKVI',
        interestedFields: ['Computer Science'],
        interestedCountries: ['Canada'],
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        notes: 'Test notes',
        counsellorId: null,
        agentId: null,
        lastInstituteAttended: 'Test University',
        lastInstituteDegree: 'Bachelor',
        interestedDegreeLevel: 'MASTERS',
        referralCode: null,
        status: 'LEAD',
        subStatus: 'EMAIL_SENT',
        createdAt: new Date(),
        updatedAt: new Date(),
        officeId: null,
      });

      prismaMock.event.count.mockResolvedValue(2);
      prismaMock.event.findMany.mockResolvedValue(mockEvents);

      const result = await service.getStudentEvents(studentId, {
        page: 1,
        limit: 10,
        sort: 'desc',
      });

      expect(result.data).toEqual(mockEvents);
      expect(result.meta).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });

      expect(prismaMock.event.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            AND: [{ type: 'STUDENT', entityId: studentId }, {}, {}, {}],
          },
        }),
      );
    });
  });
});
