# Event Logger System Documentation

## Overview
The Event Logger system is a comprehensive logging utility that tracks all activities and updates happening on the platform. It provides detailed information about who performed what action, when it was performed, and what changes were made.

## Database Schema

### Event Table
The main table that stores all event information with the following structure:

```typescript
{
  "event_id": "uuid",          // Unique identifier for the event
  "timestamp": "iso8601",      // When the event occurred
  "event_type": "string",      // Type of event (see EventType enum)
  "actor": {                   // Who performed the action
    "user_id": "uuid",
    "role": "string",
    "ip_address": "string",    // v2 feature
    "device_id": "string"      // v2 feature
  },
  "target_entity": {           // What was affected
    "type": "string",
    "id": "uuid",
    "field_modified": ["string"]
  },
  "before_snapshot": "json",    // State before the change
  "after_snapshot": "json",     // State after the change
  "context": {                  // Additional context
    "session_id": "string",     // v2 feature
    "browser_user_agent": "string", // v2 feature
    "geolocation": "string"     // v2 feature
  },
  "parent_event_id": "uuid"     // For related events
}
```

## Event Types
Events are categorized into several groups:
1. CRUD Operations - Track create, update, delete, and restore actions
2. User Actions - Track user interactions and role changes
3. Document Actions - Track file uploads and deletions
4. Status Changes - Track changes in application and student statuses
5. Communication - Track notes, comments, and emails
6. System Events (v2) - Track automated processes
7. Security Events (v2) - Track security-related activities
8. Integration Events (v2) - Track third-party integrations

## Usage

### Basic Event Logging
```typescript
await eventLoggerService.logEvent({
  eventType: EventType.CREATE_STUDENT,
  actor: {
    userId: "user-123",
    role: "COUNSELLOR"
  },
  targetEntity: {
    type: "STUDENT",
    entityId: "student-123",
    fieldModified: ["name", "email"]
  },
  beforeSnapshot: { name: "John" },
  afterSnapshot: { name: "John Doe" }
});
```

### Student-Specific Event Logging
```typescript
await eventLoggerService.logStudentEvent(
  EventType.UPDATE_STUDENT,
  "student-123",
  { userId: "user-123", role: "COUNSELLOR" },
  ["status"],
  { status: "PENDING" },
  { status: "ENROLLED" }
);
```

### Retrieving Events
```typescript
// Get all events for a student
const events = await eventLoggerService.getStudentEvents("student-123");
```

## API Endpoints

### GET /events/student/:studentId
Retrieves all events related to a specific student.

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sort`: Sort direction for timestamp (asc/desc)
- `type`: Filter by event type

### GET /events
Retrieves all events with filtering capabilities.

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sort`: Sort direction for timestamp (asc/desc)
- `type`: Filter by event type
- `entityType`: Filter by entity type
- `startDate`: Filter events after this date
- `endDate`: Filter events before this date

## V2 Features (Future Implementation)
1. IP Address Tracking
2. Device ID Tracking
3. Session Management
4. Browser User Agent Tracking
5. Geolocation Tracking
6. Advanced Security Events
7. Integration Events
8. System Events 