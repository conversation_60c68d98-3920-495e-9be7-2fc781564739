import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class GetUniversityDto {
  @ApiProperty({
    description: 'The id of the University',
    type: String,
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the University',
    type: String,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The description of the University',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'The location of the University',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  location: string;

  @ApiProperty({
    description: 'The ranking of the University',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsInt()
  ranking?: number;

  @ApiProperty({
    description: 'The creation date of the University',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'The last update date of the University',
    type: Date,
  })
  updatedAt: Date;
}
