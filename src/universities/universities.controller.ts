import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { CreateUniversityDto } from './dto/create-university.dto';
import { GetUniversityDto } from './dto/get-university.dto';
import { UpdateUniversityDto } from './dto/update-university.dto';
import { UniversitiesService } from './universities.service';

@ApiTags('Universities')
@ApiBearerAuth()
@Controller('universities')
export class UniversitiesController {
  constructor(private readonly universitiesService: UniversitiesService) {}

  @Post()
  @Permissions('create:universities')
  @ApiOperation({ summary: 'Create a new University' })
  @ApiResponse({
    status: 201,
    description: 'The University has been successfully created.',
    type: GetUniversityDto,
  })
  async createUniversity(
    @Body() createUniversityDto: CreateUniversityDto,
  ): Promise<GetUniversityDto> {
    try {
      return this.universitiesService.createUniversity(createUniversityDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @Permissions('read:universities')
  @ApiOperation({ summary: 'Get all Universities' })
  @ApiResponse({
    status: 200,
    description: 'The list of Universities has been successfully retrieved.',
    type: [GetUniversityDto],
  })
  async getAllUniversities(): Promise<GetUniversityDto[]> {
    try {
      return this.universitiesService.getAllUniversities();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @Permissions('read:universities')
  @ApiOperation({ summary: 'Get a university by ID' })
  @ApiResponse({
    status: 200,
    description: 'The University has been successfully retrieved.',
    type: GetUniversityDto,
  })
  async getUniversityById(@Param('id') id: string): Promise<GetUniversityDto> {
    try {
      return this.universitiesService.getUniversityById(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @Permissions('update:universities')
  @ApiOperation({ summary: 'Update a University by ID' })
  @ApiResponse({
    status: 200,
    description: 'The University has been successfully updated.',
    type: GetUniversityDto,
  })
  async updateUniversity(
    @Param('id') id: string,
    @Body() updateUniversityDto: UpdateUniversityDto,
  ): Promise<GetUniversityDto> {
    try {
      return this.universitiesService.updateUniversity(id, updateUniversityDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @Permissions('delete:universities')
  @ApiOperation({ summary: 'Delete a University by ID' })
  @ApiResponse({
    status: 204,
    description: 'The University has been successfully deleted.',
  })
  async deleteUniversity(@Param('id') id: string): Promise<GetUniversityDto> {
    try {
      return this.universitiesService.deleteUniversity(id);
    } catch (error) {
      throw error;
    }
  }
}
