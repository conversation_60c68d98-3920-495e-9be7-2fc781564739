import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateUniversityDto } from './dto/create-university.dto';
import { GetUniversityDto } from './dto/get-university.dto';
import { UpdateUniversityDto } from './dto/update-university.dto';

@Injectable()
export class UniversitiesService {
  constructor(private readonly prisma: PrismaService) {}

  async createUniversity(
    createUniversityDto: CreateUniversityDto,
  ): Promise<GetUniversityDto> {
    try {
      const university = await this.prisma.university.findUnique({
        where: { name: createUniversityDto.name },
      });

      if (university) {
        throw new ConflictException('University already exists.');
      }

      const newUniversity = await this.prisma.university.create({
        data: { ...createUniversityDto },
      });

      return newUniversity;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('University already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllUniversities(): Promise<GetUniversityDto[]> {
    try {
      return this.prisma.university.findMany();
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getUniversityById(id: string): Promise<GetUniversityDto> {
    try {
      const university = await this.prisma.university.findUnique({
        where: { id },
      });
      if (!university) {
        throw new NotFoundException('University not found');
      }
      return university;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateUniversity(
    id: string,
    updateUniversityDto: UpdateUniversityDto,
  ): Promise<GetUniversityDto> {
    try {
      const university = await this.prisma.university.findUnique({
        where: { id },
      });
      if (!university) {
        throw new NotFoundException('University not found');
      }

      return this.prisma.university.update({
        where: { id },
        data: { ...updateUniversityDto },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteUniversity(id: string): Promise<GetUniversityDto> {
    try {
      const university = await this.prisma.university.findUnique({
        where: { id },
      });
      if (!university) {
        throw new NotFoundException('University not found');
      }

      await this.prisma.university.delete({ where: { id } });

      return university;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
