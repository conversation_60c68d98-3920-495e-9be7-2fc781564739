import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateOfficeDto } from './dto/create-office.dto';
import { GetOfficeDto } from './dto/get-a-office.dto';
import { GetOfficesDto } from './dto/get-offices.dto';
import { OfficeEmployeesDTO } from './dto/office-employees.dto';
import { UpdateOfficeDto } from './dto/update-office.dto';

@Injectable()
export class OfficesService {
  constructor(private prisma: PrismaService) {}
  async create(createOfficeDto: CreateOfficeDto): Promise<GetOfficeDto> {
    try {
      return await this.prisma.office.create({
        data: {
          name: createOfficeDto.name,
          cityId: createOfficeDto.cityId,
        },
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(): Promise<GetOfficesDto[]> {
    try {
      return await this.prisma.office.findMany({
        include: { city: true },
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<GetOfficeDto> {
    try {
      const office = await this.prisma.office.findUnique({
        where: { id },
      });

      if (!office) {
        throw new HttpException(
          `Office with ID ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return office;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: string,
    updateOfficeDto: UpdateOfficeDto,
  ): Promise<GetOfficeDto> {
    try {
      const existingOffice = await this.prisma.office.findUnique({
        where: { id },
      });

      if (!existingOffice) {
        throw new HttpException(
          `Office with ID ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return await this.prisma.office.update({
        where: { id },
        data: {
          name: updateOfficeDto.name,
          cityId: updateOfficeDto.cityId,
        },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(id: string): Promise<GetOfficeDto> {
    try {
      const office = await this.prisma.office.findUnique({
        where: { id },
      });

      if (!office) {
        throw new HttpException(
          `Office with ID ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return await this.prisma.office.delete({
        where: { id },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findEmployeesByOfficeId(id: string): Promise<OfficeEmployeesDTO> {
    try {
      const employees = await this.prisma.office.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          cityId: true,
          countryId: true,
          regionId: true,
          employees: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!employees) {
        throw new NotFoundException(`Employee with Office ID ${id} not found`);
      }
      return employees;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
