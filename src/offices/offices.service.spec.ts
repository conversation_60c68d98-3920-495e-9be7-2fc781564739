import { Test, TestingModule } from '@nestjs/testing';
import { OfficesService } from './offices.service';
import { PrismaService } from '../prisma/prisma.service';
import { HttpException, NotFoundException } from '@nestjs/common';

describe('OfficesService', () => {
  let service: OfficesService;
  let prisma: PrismaService;

  const mockPrismaService = {
    office: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OfficesService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<OfficesService>(OfficesService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  describe('create', () => {
    it('should create an office successfully', async () => {
      const dto = { name: 'Test Office', cityId: 'city123' };
      const expectedResponse = { id: '1', ...dto };

      mockPrismaService.office.create.mockResolvedValue(expectedResponse);
      await expect(service.create(dto)).resolves.toEqual(expectedResponse);
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.create.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        service.create({ name: 'Test Office', cityId: 'city123' }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findAll', () => {
    it('should return an array of offices', async () => {
      const offices = [{ id: '1', name: 'Office 1', cityId: 'city123' }];
      mockPrismaService.office.findMany.mockResolvedValue(offices);

      await expect(service.findAll()).resolves.toEqual(offices);
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.findMany.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.findAll()).rejects.toThrow(HttpException);
    });
  });

  describe('findOne', () => {
    it('should return an office when it exists', async () => {
      const office = { id: '1', name: 'Office 1', cityId: 'city123' };
      mockPrismaService.office.findUnique.mockResolvedValue(office);

      await expect(service.findOne('1')).resolves.toEqual(office);
    });

    it('should throw an error when the office is not found', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(HttpException);
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.findUnique.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.findOne('1')).rejects.toThrow(HttpException);
    });
  });

  describe('update', () => {
    it('should update an existing office successfully', async () => {
      const updateDto = { name: 'Updated Office', cityId: 'city123' };
      const existingOffice = { id: '1', name: 'Office 1', cityId: 'city123' };
      const updatedOffice = { id: '1', ...updateDto };

      mockPrismaService.office.findUnique.mockResolvedValue(existingOffice);
      mockPrismaService.office.update.mockResolvedValue(updatedOffice);

      await expect(service.update('1', updateDto)).resolves.toEqual(
        updatedOffice,
      );
    });

    it('should throw an error when the office is not found', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue(null);

      await expect(
        service.update('1', { name: 'Updated', cityId: 'city123' }),
      ).rejects.toThrow(HttpException);
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue({ id: '1' });
      mockPrismaService.office.update.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        service.update('1', { name: 'Updated', cityId: 'city123' }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('remove', () => {
    it('should delete an office successfully', async () => {
      const office = { id: '1', name: 'Office 1', cityId: 'city123' };

      mockPrismaService.office.findUnique.mockResolvedValue(office);
      mockPrismaService.office.delete.mockResolvedValue(office);

      await expect(service.remove('1')).resolves.toEqual(office);
    });

    it('should throw an error when the office is not found', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue(null);

      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue({ id: '1' });
      mockPrismaService.office.delete.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });
  });

  describe('findEmployeesByOfficeId', () => {
    it('should return employees for a given office ID', async () => {
      const employees = {
        id: '1',
        name: 'Office 1',
        cityId: 'city123',
        employees: [
          {
            id: 'emp1',
            user: { id: 'u1', name: 'User 1', email: '<EMAIL>' },
          },
        ],
      };

      mockPrismaService.office.findUnique.mockResolvedValue(employees);
      await expect(service.findEmployeesByOfficeId('1')).resolves.toEqual(
        employees,
      );
    });

    it('should throw an error when the office is not found', async () => {
      mockPrismaService.office.findUnique.mockResolvedValue(null);

      await expect(service.findEmployeesByOfficeId('1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw an error when PrismaService fails', async () => {
      mockPrismaService.office.findUnique.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.findEmployeesByOfficeId('1')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
