import { Test, TestingModule } from '@nestjs/testing';
import { OfficesController } from './offices.controller';
import { OfficesService } from './offices.service';

import { NotFoundException } from '@nestjs/common';
import { GetOfficeDto } from './dto/get-a-office.dto';
import { GetOfficesDto } from './dto/get-offices.dto';
import { CreateOfficeDto } from './dto/create-office.dto';
import { UpdateOfficeDto } from './dto/update-office.dto';
import { OfficeEmployeesDTO } from './dto/office-employees.dto';

describe('OfficesController', () => {
  let controller: OfficesController;
  let service: OfficesService;

  const mockOffice: GetOfficeDto = {
    id: '1',
    name: 'Test Office',
    cityId: 'q22123',
  };

  const mockOffices: GetOfficesDto[] = [mockOffice];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OfficesController],
      providers: [
        {
          provide: OfficesService,
          useValue: {
            create: jest.fn().mockResolvedValue(mockOffice),
            findAll: jest.fn().mockResolvedValue(mockOffices),
            findOne: jest.fn().mockResolvedValue(mockOffice),
            update: jest.fn().mockResolvedValue(mockOffice),
            remove: jest.fn().mockResolvedValue(mockOffice),
            findEmployeesByOfficeId: jest.fn().mockResolvedValue([]),
          },
        },
      ],
    }).compile();

    controller = module.get<OfficesController>(OfficesController);
    service = module.get<OfficesService>(OfficesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new office', async () => {
      const createDto: CreateOfficeDto = {
        name: 'New Office',
        cityId: '**********',
      };
      await expect(controller.create(createDto)).resolves.toEqual(mockOffice);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('findAll', () => {
    it('should return an array of offices', async () => {
      await expect(controller.findAll()).resolves.toEqual(mockOffices);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return an office by id', async () => {
      await expect(controller.findOne('1')).resolves.toEqual(mockOffice);
      expect(service.findOne).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException when office not found', async () => {
      jest.spyOn(service, 'findOne').mockRejectedValue(new NotFoundException());
      await expect(controller.findOne('99')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update an office', async () => {
      const updateDto: UpdateOfficeDto = {
        name: 'Updated Office',
        cityId: '**********',
      };
      await expect(controller.update('1', updateDto)).resolves.toEqual(
        mockOffice,
      );
      expect(service.update).toHaveBeenCalledWith('1', updateDto);
    });
  });

  describe('remove', () => {
    it('should remove an office', async () => {
      await expect(controller.remove('1')).resolves.toEqual(mockOffice);
      expect(service.remove).toHaveBeenCalledWith('1');
    });
  });

  describe('findEmployeesByOfficeId', () => {
    it('should return employees of an office', async () => {
      const employees = [
        {
          id: 'asdsad',
          user: {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
        },
      ];

      const officeEmployeesDto: OfficeEmployeesDTO = {
        id: '1',
        name: 'Office Name',
        cityId: '123',
        employees: employees, // Assign employees inside the DTO
      };

      jest
        .spyOn(service, 'findEmployeesByOfficeId')
        .mockResolvedValue(officeEmployeesDto);

      await expect(controller.findCitiesByRegionId('1')).resolves.toEqual(
        officeEmployeesDto,
      );

      expect(service.findEmployeesByOfficeId).toHaveBeenCalledWith('1');
    });
  });
});
