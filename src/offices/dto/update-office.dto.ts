import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateOfficeDto {
  @ApiProperty({
    description: 'Office name',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'City ID',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  cityId?: string;
}
