import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class DeleteOfficeResponseDto {
  @ApiProperty({
    description: 'Office ID',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Success message',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  message: string;
}
