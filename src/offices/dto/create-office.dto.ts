import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateOfficeDto {
  @ApiProperty({
    description: 'Office name',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'City ID of the Office',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  cityId: string;
}
