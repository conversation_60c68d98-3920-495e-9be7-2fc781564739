import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class GetOfficeDto {
  @ApiProperty({
    description: 'Office ID',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Office Name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'City ID of the Office',
    required: false,
    type: String,
  })
  @IsString()
  @IsOptional()
  cityId: string;
}
