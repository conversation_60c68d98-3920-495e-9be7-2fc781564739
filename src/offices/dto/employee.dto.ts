import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsString, IsUUID, ValidateNested } from 'class-validator';
import { UserDTO } from './user.dto';

export class EmployeeDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the Employee',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'List of Employees in the Office',
    type: UserDTO,
    required: false,
  })
  @ValidateNested()
  @Type(() => UserDTO)
  user?: UserDTO;
}
