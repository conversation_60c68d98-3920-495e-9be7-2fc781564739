import { ApiProperty } from '@nestjs/swagger';
import { Country } from '@prisma/client';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { GetACityDto } from 'src/cities/dto/get-a-city.dto';
import { GetRegionDto } from 'src/regions/dto/get-a-region.dto';

export class GetOfficesDto {
  @ApiProperty({
    description: 'Office ID',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Office Name',
    required: true,
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'City ID of the Office',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  cityId: string;

  @ApiProperty({ type: () => GetACityDto, required: false })
  @IsOptional()
  city?: GetACityDto;

  @ApiProperty({ type: () => GetRegionDto, required: false })
  @IsOptional()
  region?: GetRegionDto;
}
