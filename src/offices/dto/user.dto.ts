import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class UserDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the Employee',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 'string',
    description: 'Name of the Employee',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'string',
    description: 'Email of the Employee',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  email: string;
}
