import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { EmployeeDTO } from './employee.dto';

export class OfficeEmployeesDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the Office',
    required: true,
    type: String,
  })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 'string',
    description: 'Name of the Office',
    required: true,
    type: String,
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'string',
    description: 'City ID the Office belongs to',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  cityId?: string;

  @ApiProperty({
    example: 'string',
    description: 'Country ID the Office belongs to',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  countryId?: string;

  @ApiProperty({
    example: 'string',
    description: 'ID of City the Office belongs to',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  regionId?: string;

  @ApiProperty({
    description: 'List of Employees in the Office',
    type: [EmployeeDTO],
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => EmployeeDTO)
  employees?: EmployeeDTO[];
}
