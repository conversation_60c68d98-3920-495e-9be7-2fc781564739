import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { CreateOfficeDto } from './dto/create-office.dto';
import { GetOfficeDto } from './dto/get-a-office.dto';
import { GetOfficesDto } from './dto/get-offices.dto';
import { OfficeEmployeesDTO } from './dto/office-employees.dto';
import { UpdateOfficeDto } from './dto/update-office.dto';
import { OfficesService } from './offices.service';

@ApiBearerAuth()
@ApiTags('Offices')
@Controller('offices')
export class OfficesController {
  constructor(private readonly officesService: OfficesService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: ' A new Office created successfully.',
    type: GetOfficeDto,
  })
  @Permissions('create:offices')
  async create(
    @Body() createOfficeDto: CreateOfficeDto,
  ): Promise<GetOfficeDto> {
    try {
      return await this.officesService.create(createOfficeDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Offices.',
    type: [GetOfficeDto],
  })
  @Permissions('read:offices')
  async findAll(): Promise<GetOfficesDto[]> {
    try {
      return await this.officesService.findAll();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved an Office.',
    type: GetOfficeDto,
  })
  @Permissions('read:offices')
  async findOne(@Param('id') id: string): Promise<GetOfficeDto> {
    try {
      return await this.officesService.findOne(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Office updated successfully.',
    type: UpdateOfficeDto,
  })
  @Permissions('update:offices')
  async update(
    @Param('id') id: string,
    @Body() updateOfficeDto: UpdateOfficeDto,
  ): Promise<GetOfficeDto> {
    try {
      return await this.officesService.update(id, updateOfficeDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'Office deleted successfully.',
    type: GetOfficeDto,
  })
  @Permissions('delete:offices')
  async remove(@Param('id') id: string): Promise<GetOfficeDto> {
    try {
      return await this.officesService.remove(id);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id/employees')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved Office Employees.',
    type: OfficeEmployeesDTO,
  })
  @Permissions('read:offices')
  async findCitiesByRegionId(
    @Param('id') id: string,
  ): Promise<OfficeEmployeesDTO> {
    try {
      return await this.officesService.findEmployeesByOfficeId(id);
    } catch (error) {
      throw error;
    }
  }
}
