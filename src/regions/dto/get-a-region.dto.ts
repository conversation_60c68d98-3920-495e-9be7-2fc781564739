import { ApiProperty } from '@nestjs/swagger';
import { Country } from '@prisma/client';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class GetRegionDto {
  @ApiProperty({
    description: 'Region ID',
    type: String,
    required: true,
  })
  @IsUUID()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Region Name',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Country ID of the region',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  countryId: string;

  @ApiProperty({ type: () => String, required: false })
  @IsOptional()
  country?: Country;
}
