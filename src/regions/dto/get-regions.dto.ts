import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class GetRegionsDto {
  @ApiProperty({
    description: 'Region ID',
    required: true,
    type: String,
  })
  @IsUUID()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Region Name',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Country ID of the Region',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  countryId: string;
}
