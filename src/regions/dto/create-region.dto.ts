import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateRegionDto {
  @ApiProperty({
    description: 'Region Name',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Country ID of the Region',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  countryId: string;
}
