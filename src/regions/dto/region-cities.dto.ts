import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { CitiesDTO } from './cities.dto';

export class RegionCitiesDTO {
  @ApiProperty({
    example: 'string',
    description: 'Unique ID of the Region',
    type: String,
    required: true,
  })
  @IsUUID()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'string',
    description: 'Name of the Region',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'string',
    description: 'ID of the country the Region belongs to',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  countryId?: string;

  @ValidateNested({ each: true })
  @Type(() => CitiesDTO)
  cities?: CitiesDTO[];
}
