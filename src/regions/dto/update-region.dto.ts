import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateRegionDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'Region ID',
    type: String,
    required: false,
  })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'Country ID',
    type: String,
    required: false,
  })
  countryId?: string;
}
