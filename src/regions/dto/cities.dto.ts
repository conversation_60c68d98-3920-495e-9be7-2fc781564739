import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CitiesDTO {
  @ApiProperty({
    example: 'string',
    type: String,
    description: 'Unique ID of the City',
    required: true,
  })
  @IsUUID()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'string',
    type: String,
    description: 'Name of the City',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'string',
    type: String,
    description: 'Region Id of the City',
    required: false,
  })
  @IsString()
  @IsOptional()
  regionId: string;
}
