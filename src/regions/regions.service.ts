import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { PrismaService } from 'src/prisma/prisma.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { GetRegionDto } from './dto/get-a-region.dto';
import { RegionCitiesDTO } from './dto/region-cities.dto';
import { UpdateRegionDto } from './dto/update-region.dto';

@Injectable()
export class RegionsService {
  constructor(private prisma: PrismaService) {}

  // Create a new Region
  async create(createRegionDto: CreateRegionDto): Promise<GetRegionDto> {
    try {
      return await this.prisma.region.create({
        data: {
          name: createRegionDto.name,
          countryId: createRegionDto.countryId,
        },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Get all Regions
  async findAll(): Promise<GetRegionDto[]> {
    try {
      return await this.prisma.region.findMany({
        include: { country: true },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Get a Region by ID
  async findOne(id: string): Promise<GetRegionDto> {
    try {
      const region = await this.prisma.region.findUnique({
        where: { id },
      });

      if (!region) {
        throw new NotFoundException(`Region with ID ${id} not found`);
      }

      return region;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Update a Region
  async update(
    id: string,
    updateRegionDto: UpdateRegionDto,
  ): Promise<GetRegionDto> {
    try {
      const existingRegion = await this.prisma.region.findUnique({
        where: { id },
      });

      if (!existingRegion) {
        throw new NotFoundException(`Region with ID ${id} not found`);
      }

      return await this.prisma.region.update({
        where: { id },
        data: {
          name: updateRegionDto.name,
          countryId: updateRegionDto.countryId,
        },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Delete a Region
  async remove(id: string): Promise<GetRegionDto> {
    try {
      const region = await this.prisma.region.findUnique({
        where: { id },
      });

      if (!region) {
        throw new NotFoundException(`Region with ID ${id} not found`);
      }

      return await this.prisma.region.delete({
        where: { id },
      });
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async findCitiesByRegionId(id: string): Promise<RegionCitiesDTO> {
    try {
      const region = await this.prisma.region.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          countryId: true,
          cities: {
            select: {
              id: true,
              name: true,
              regionId: true,
            },
          },
        },
      });

      if (!region) {
        throw new NotFoundException(`Cities with Region ID ${id} not found`);
      }
      return region;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
