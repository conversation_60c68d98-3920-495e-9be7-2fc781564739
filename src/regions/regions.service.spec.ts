import { Test, TestingModule } from '@nestjs/testing';
import { RegionsService } from './regions.service';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionCitiesDTO } from './dto/region-cities.dto';
import { NotFoundException, HttpException, HttpStatus } from '@nestjs/common';
import { GetRegionDto } from './dto/get-a-region.dto';

describe('RegionsService', () => {
  let service: RegionsService;
  let prisma: PrismaService;

  const mockRegion: GetRegionDto = {
    id: '1',
    name: 'Test Region',
    countryId: '10',
  };

  const mockRegions: GetRegionDto[] = [mockRegion];
  const mockCities = [{ id: '1', name: 'Test City', regionId: '1' }];

  const mockPrismaService = {
    region: {
      create: jest.fn().mockResolvedValue(mockRegion),
      findMany: jest.fn().mockResolvedValue(mockRegions),
      findUnique: jest.fn().mockImplementation(({ where }) => {
        if (where.id === '1') return mockRegion;
        return null;
      }),
      update: jest.fn().mockResolvedValue(mockRegion),
      delete: jest.fn().mockResolvedValue(mockRegion),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RegionsService,
        { provide: PrismaService, useValue: mockPrismaService },
      ],
    }).compile();

    service = module.get<RegionsService>(RegionsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a region', async () => {
    const createDto: CreateRegionDto = { name: 'Test Region', countryId: '10' };
    await expect(service.create(createDto)).resolves.toEqual(mockRegion);
    expect(prisma.region.create).toHaveBeenCalledWith({ data: createDto });
  });

  it('should return all regions', async () => {
    await expect(service.findAll()).resolves.toEqual(mockRegions);
    expect(prisma.region.findMany).toHaveBeenCalled();
  });

  it('should return a region by ID', async () => {
    await expect(service.findOne('1')).resolves.toEqual(mockRegion);
    expect(prisma.region.findUnique).toHaveBeenCalledWith({
      where: { id: '1' },
    });
  });

  it('should throw NotFoundException when region is not found', async () => {
    await expect(service.findOne('999')).rejects.toThrow(NotFoundException);
  });

  it('should update a region', async () => {
    const updateDto: UpdateRegionDto = { name: 'Updated Region' };
    await expect(service.update('1', updateDto)).resolves.toEqual(mockRegion);
    expect(prisma.region.update).toHaveBeenCalledWith({
      where: { id: '1' },
      data: updateDto,
    });
  });

  it('should delete a region', async () => {
    await expect(service.remove('1')).resolves.toEqual(mockRegion);
    expect(prisma.region.delete).toHaveBeenCalledWith({ where: { id: '1' } });
  });

  it('should return cities by region ID', async () => {
    prisma.region.findUnique = jest.fn().mockResolvedValue({
      ...mockRegion,
      cities: mockCities,
    });

    await expect(service.findCitiesByRegionId('1')).resolves.toEqual({
      ...mockRegion,
      cities: mockCities,
    });
    expect(prisma.region.findUnique).toHaveBeenCalledWith({
      where: { id: '1' },
      select: {
        id: true,
        name: true,
        countryId: true,
        cities: { select: { id: true, name: true, regionId: true } },
      },
    });
  });

  it('should throw NotFoundException when cities for region ID not found', async () => {
    prisma.region.findUnique = jest.fn().mockResolvedValue(null);
    await expect(service.findCitiesByRegionId('999')).rejects.toThrow(
      NotFoundException,
    );
  });
});
