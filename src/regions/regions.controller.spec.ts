import { Test, TestingModule } from '@nestjs/testing';
import { RegionsController } from './regions.controller';
import { RegionsService } from './regions.service';
import { CreateRegionDto } from './dto/create-region.dto';

import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionCitiesDTO } from './dto/region-cities.dto';
import { CitiesDTO } from './dto/cities.dto';
import { GetRegionDto } from './dto/get-a-region.dto';

describe('RegionsController', () => {
  let controller: RegionsController;
  let service: RegionsService;

  const mockRegion: GetRegionDto = {
    id: '1',
    name: 'Test Region',
    countryId: '10',
  };

  const mockRegions: GetRegionDto[] = [mockRegion];
  const mockCities: CitiesDTO[] = [
    { id: '1', name: 'Test City', regionId: '1' },
  ];

  const mockRegionsService = {
    create: jest.fn().mockResolvedValue(mockRegion),
    findAll: jest.fn().mockResolvedValue(mockRegions),
    findOne: jest.fn().mockResolvedValue(mockRegion),
    update: jest.fn().mockResolvedValue(mockRegion),
    remove: jest.fn().mockResolvedValue(mockRegion),
    findCitiesByRegionId: jest
      .fn()
      .mockResolvedValue({ ...mockRegion, cities: mockCities }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RegionsController],
      providers: [{ provide: RegionsService, useValue: mockRegionsService }],
    }).compile();

    controller = module.get<RegionsController>(RegionsController);
    service = module.get<RegionsService>(RegionsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a region', async () => {
    const createDto: CreateRegionDto = { name: 'Test Region', countryId: '10' };
    await expect(controller.create(createDto)).resolves.toEqual(mockRegion);
    expect(service.create).toHaveBeenCalledWith(createDto);
  });

  it('should return all regions', async () => {
    await expect(controller.findAll()).resolves.toEqual(mockRegions);
    expect(service.findAll).toHaveBeenCalled();
  });

  it('should return a region by ID', async () => {
    await expect(controller.findOne('1')).resolves.toEqual(mockRegion);
    expect(service.findOne).toHaveBeenCalledWith('1');
  });

  it('should update a region', async () => {
    const updateDto: UpdateRegionDto = { name: 'Updated Region' };
    await expect(controller.update('1', updateDto)).resolves.toEqual(
      mockRegion,
    );
    expect(service.update).toHaveBeenCalledWith('1', updateDto);
  });

  it('should delete a region', async () => {
    await expect(controller.remove('1')).resolves.toEqual(mockRegion);
    expect(service.remove).toHaveBeenCalledWith('1');
  });

  it('should return cities by region ID', async () => {
    await expect(controller.findCitiesByRegionId('1')).resolves.toEqual({
      ...mockRegion,
      cities: mockCities,
    });
    expect(service.findCitiesByRegionId).toHaveBeenCalledWith('1');
  });
});
