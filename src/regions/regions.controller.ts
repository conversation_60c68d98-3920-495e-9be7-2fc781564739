import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { CreateRegionDto } from './dto/create-region.dto';
import { GetRegionDto } from './dto/get-a-region.dto';
import { GetRegionsDto } from './dto/get-regions.dto';
import { RegionCitiesDTO } from './dto/region-cities.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionsService } from './regions.service';

@ApiBearerAuth()
@Controller('regions')
@ApiTags('Regions')
export class RegionsController {
  constructor(private readonly regionsService: RegionsService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'Region created successfully.',
    type: CreateRegionDto,
  })
  @Permissions('create:regions')
  async create(
    @Body() createRegionDto: CreateRegionDto,
  ): Promise<GetRegionDto> {
    try {
      return await this.regionsService.create(createRegionDto);
    } catch (error) {
      throw new Error('Error creating region: ' + error.message);
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved Regions.',
    type: [GetRegionsDto],
  })
  @Permissions('read:regions')
  async findAll(): Promise<GetRegionsDto[]> {
    try {
      return await this.regionsService.findAll();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a region.',
    type: GetRegionDto,
  })
  @Permissions('read:regions')
  async findOne(@Param('id') id: string): Promise<GetRegionDto> {
    try {
      return await this.regionsService.findOne(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Region updated successfully.',
    type: UpdateRegionDto,
  })
  @Permissions('update:regions')
  async update(
    @Param('id') id: string,
    @Body() updateRegionDto: UpdateRegionDto,
  ): Promise<GetRegionDto> {
    try {
      return await this.regionsService.update(id, updateRegionDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'Region deleted successfully.',
    type: GetRegionDto,
  })
  @Permissions('delete:regions')
  async remove(@Param('id') id: string): Promise<GetRegionDto> {
    try {
      return await this.regionsService.remove(id);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id/cities')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved Cites of a Region.',
    type: RegionCitiesDTO,
  })
  @Permissions('read:regions')
  async findCitiesByRegionId(
    @Param('id') id: string,
  ): Promise<RegionCitiesDTO> {
    try {
      return await this.regionsService.findCitiesByRegionId(id);
    } catch (error) {
      throw error;
    }
  }
}
