import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { PrismaService } from '../prisma/prisma.service';
import { PageMetaDto } from '../common/dtos/pageMeta.dto';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { PageDto } from '../common/dtos/page.dto';
import { Permission } from '@prisma/client';

@Injectable()
export class PermissionsService {
  constructor(private readonly prisma: PrismaService) {}

  create(createPermissionDto: CreatePermissionDto) {
    return this.prisma.permission.create({
      data: createPermissionDto,
    });
  }

  async findAll(pageOptionsDto: PageOptionsDto) {
    const { skip, take = 50 } = pageOptionsDto;
    const [data, itemCount] = await Promise.all([
      this.prisma.permission.findMany({
        take,
        skip,
      }),
      this.prisma.permission.count(),
    ]);

    const meta = new PageMetaDto({ itemCount, pageOptionsDto });
    return new PageDto<Permission>(data, meta);
  }

  findOne(id: string) {
    return this.prisma.permission.findUnique({
      where: { id: id },
    });
  }

  update(id: string, updatePermissionDto: UpdatePermissionDto) {
    return this.prisma.permission.update({
      where: { id: id },
      data: updatePermissionDto,
    });
  }

  remove(id: string) {
    return this.prisma.permission.delete({
      where: { id: id },
    });
  }

  async assignPermissionsToRole(roleId: string, permissionIds: string[]) {
    try {
      await this.prisma.role.update({
        where: { id: roleId },
        data: {
          permissions: {
            connect: permissionIds.map((id) => ({ id })),
          },
        },
      });
      return { message: 'Permissions assigned successfully.' };
    } catch (error) {
      throw new InternalServerErrorException('Failed to assign permissions.');
    }
  }
}
