import { <PERSON>, Controller, Get, Param, <PERSON>, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { CreateWorkflowDto } from './dto/create-workflow.dto';
import { UpdateWorkflowDto } from './dto/update-workflow.dto';
import { WorkflowDto } from './dto/workflow.dto';
import { WorkflowsService } from './workflows.service';

@ApiBearerAuth()
@ApiTags('Workflows')
@Controller('workflows')
export class WorkflowsController {
  constructor(private readonly workflowService: WorkflowsService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new Workflow has been created successfully',
    type: WorkflowDto,
  })
  @Permissions('create:workflows')
  createWorkflow(
    @Body() createWorkflowDto: CreateWorkflowDto,
  ): Promise<WorkflowDto> {
    try {
      return this.workflowService.createWorkflow(createWorkflowDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all Workflows',
    type: [WorkflowDto],
  })
  @Permissions('create:workflows')
  getAllWorkflows(): Promise<WorkflowDto[]> {
    try {
      return this.workflowService.getAllWorkflows();
    } catch (error) {
      throw error;
    }
  }

  @Get('/schema')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Schema structure',
  })
  @Permissions('read:workflows')
  getSchemaStructure(): Promise<any> {
    try {
      return this.workflowService.getSchemaStructure();
    } catch (error) {
      throw error;
    }
  }

  @Get('/actions')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Actions details',
  })
  @Permissions('read:workflows')
  getActionsDetails(): Promise<any> {
    try {
      return this.workflowService.getActionsDetails();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a Workflow by id',
    type: WorkflowDto,
  })
  @Permissions('read:workflows')
  getWorkflowById(@Param('id') id: string): Promise<WorkflowDto> {
    try {
      return this.workflowService.getWorkflowById(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully updated a Workflow by id',
    type: WorkflowDto,
  })
  @Permissions('update:workflows')
  updateWorkflow(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
  ): Promise<WorkflowDto> {
    try {
      return this.workflowService.updateWorkflow(id, updateWorkflowDto);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id/toggle')
  @ApiResponse({
    status: 200,
    description: 'Successfully toggled a Workflow by id',
    type: WorkflowDto,
  })
  @Permissions('read:workflows')
  toggleWorkflow(@Param('id') id: string): Promise<WorkflowDto> {
    try {
      return this.workflowService.toggleWorkflow(id);
    } catch (error) {
      throw error;
    }
  }
}
