{"actions": [{"name": "sendEmail", "description": "Send an email to the user", "parameters": [{"name": "to", "description": "The email address to send the email to", "type": "string"}, {"name": "subject", "description": "The subject of the email", "type": "string"}, {"name": "body", "description": "The body of the email", "type": "string"}]}, {"name": "triggerWebhook", "description": "Trigger a webhook", "parameters": [{"name": "url", "description": "The URL of the webhook", "type": "string"}, {"name": "payload", "description": "The payload to send to the webhook", "type": "object"}, {"name": "headers", "description": "The headers to send to the webhook", "type": "object"}]}]}