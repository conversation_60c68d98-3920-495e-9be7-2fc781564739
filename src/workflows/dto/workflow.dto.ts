import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { StepDto } from './step.dto';
import { TriggerDto } from './trigger.dto';

export class WorkflowDto {
  @ApiProperty({
    description: 'The id of the Workflow',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the Workflow',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The description of the Workflow',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'The active status of the Workflow',
    required: true,
    type: Boolean,
  })
  @IsNotEmpty()
  @IsBoolean()
  active: boolean;

  @ApiProperty({
    description: 'The Steps of the Workflow',
    required: false,
    type: [StepDto],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StepDto)
  steps?: StepDto[];

  @ApiProperty({
    description: 'The Triggers of the Workflow',
    required: false,
    type: [TriggerDto],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TriggerDto)
  triggers?: TriggerDto[];
}
