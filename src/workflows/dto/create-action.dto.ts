import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateStepDto } from './create-step.dto';

export class CreateActionDto {
  @ApiProperty({
    description: 'Type of Action (e.g., sendEmail, triggerWebhook)',
    required: true,
    type: String,
  })
  @IsNotEmpty({ message: 'Type must not be empty' })
  @IsString({ message: 'Type must be a string' })
  type: string;

  @ApiProperty({
    description: 'Details needed to execute the action as a JSON object',
    required: true,
    type: Object,
  })
  @IsNotEmpty({ message: 'Details must not be empty' })
  @IsObject({ message: 'Details must be a valid JSON object' })
  details: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Nested steps associated with the action',
    type: [CreateStepDto],
  })
  @IsOptional()
  @IsArray({ message: 'Steps must be an array' })
  @ValidateNested({
    each: true,
    message: 'Each step must be a valid Step dto object',
  })
  @Type(() => CreateStepDto)
  steps?: CreateStepDto[];
}
