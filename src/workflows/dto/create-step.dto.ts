import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { StepType } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateActionDto } from './create-action.dto';
import { CreateConditionDto } from './create-condition.dto';

export class CreateStepDto {
  @ApiProperty({
    description: 'The name of the Step',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The type of the Step (e.g. calculation or action)',
    required: true,
    enum: StepType,
  })
  @IsNotEmpty()
  @IsEnum(StepType)
  type: StepType;

  @ApiProperty({
    description: 'The details for the Step in JSON format',
    required: true,
    type: Object,
  })
  @IsNotEmpty()
  @IsObject({ message: 'Details must be a valid JSON object' })
  details: Record<string, any>;

  @ApiProperty({
    description: 'The execution order of the Step',
    required: true,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  order?: number;

  // @ApiPropertyOptional({
  //   description: 'Conditional logic for the IF branch of the step (if any)',
  //   type: CreateConditionDto,
  // })
  // @IsOptional()
  // @ValidateNested()
  // @Type(() => CreateConditionDto)
  // ifCondition?: CreateConditionDto;

  // @ApiPropertyOptional({
  //   description: 'Conditional logic for the ELSE branch of the step (if any)',
  //   type: CreateConditionDto,
  // })
  // @IsOptional()
  // @ValidateNested()
  // @Type(() => CreateConditionDto)
  // elseCondition?: CreateConditionDto;

  @ApiPropertyOptional({
    description: 'Condition details if the step is an condition type',
    type: CreateConditionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateConditionDto)
  condition?: CreateConditionDto;

  @ApiPropertyOptional({
    description: 'Action details if the step is an action type',
    type: CreateActionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateActionDto)
  action?: CreateActionDto;
}
