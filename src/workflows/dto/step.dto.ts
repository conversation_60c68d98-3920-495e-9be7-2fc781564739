import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { IsInt, IsNotEmpty, IsObject, IsString, IsUUID } from 'class-validator';

export class StepDto {
  @ApiProperty({
    description: 'The id of the Step',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the Step',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The type of the step (e.g., "calculation" or "action")',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty({
    description:
      'The details of the Step in JSON format stating what needs to be done in this Step',
    required: true,
    type: Object,
  })
  @IsNotEmpty()
  @IsObject({ message: 'Details must be a valid JSON object' })
  details: JsonValue;

  @ApiProperty({
    description: 'The order of the Step',
    required: true,
    type: Number,
  })
  @IsNotEmpty()
  @IsInt()
  order: number;
}
