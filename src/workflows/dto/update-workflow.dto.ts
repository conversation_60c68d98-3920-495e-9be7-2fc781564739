import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateStepDto } from './create-step.dto';
import { CreateTriggerDto } from './create-trigger.dto';

export class UpdateWorkflowDto {
  @ApiProperty({
    description: 'The name of the Workflow',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'The description of the Workflow',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'The active status of the Workflow',
    required: false,
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiProperty({
    description: 'The Steps of the Workflow',
    required: false,
    type: [CreateStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStepDto)
  steps?: CreateStepDto[];

  @ApiProperty({
    description: 'The Triggers of the Workflow',
    required: false,
    type: [CreateTriggerDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTriggerDto)
  triggers?: CreateTriggerDto[];
}
