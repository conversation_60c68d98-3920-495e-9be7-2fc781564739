import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ConditionType } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateStepDto } from './create-step.dto'; // Adjust the import path as needed

export class CreateConditionDto {
  @ApiProperty({
    description: 'Type of Condition (e.g., sendEmail, triggerWebhook)',
    required: true,
    enum: ConditionType,
  })
  @IsNotEmpty()
  @IsEnum(ConditionType)
  type: ConditionType;

  @ApiProperty({
    description:
      'The left value for the condition (can be a literal or field reference)',
    required: true,
    type: Object,
  })
  @IsNotEmpty()
  @IsObject()
  leftValue: Record<string, any>;

  @ApiProperty({
    description:
      'The right value for the condition (can be a literal or field reference)',
    required: true,
    type: Object,
  })
  @IsNotEmpty()
  @IsObject()
  rightValue: Record<string, any>;

  @ApiPropertyOptional({
    description: 'The operator for the condition (e.g. >, <, ==, +, -)',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  operator: string;

  @ApiProperty({
    description: 'Criteria for evaluating the condition as a JSON object',
    required: true,
    type: Object,
  })
  @IsOptional()
  @IsObject()
  criteria?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Steps to execute if the condition is true',
    type: [CreateStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStepDto)
  ifBranch?: CreateStepDto[];

  @ApiPropertyOptional({
    description: 'Steps to execute if the condition is false',
    type: [CreateStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStepDto)
  elseBranch?: CreateStepDto[];
}
