import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Actors, EventType } from '@prisma/client';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class TriggerDto {
  @ApiProperty({
    description: 'The id of the Step',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The event type of the Trigger',
    required: true,
    enum: EventType,
  })
  @IsNotEmpty()
  @IsEnum(EventType, {
    message: 'Event type must be one of the defined enum values',
  })
  event_type: EventType;

  @ApiPropertyOptional({
    description: 'The actor associated with the Trigger',
    required: true,
    enum: Actors,
  })
  @IsOptional()
  @IsEnum(Actors, { message: 'Actor must be one of the defined enum values' })
  actor: Actors;
}
