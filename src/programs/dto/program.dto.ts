import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsUUID,
} from 'class-validator';

export class ProgramDto {
  @ApiProperty({
    description: 'The id of the Program',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the Program',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The duration of the Program',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  duration: string;

  @ApiProperty({
    description: 'The type of degree awarded by the Program',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  degreeType: string;

  @ApiProperty({
    description: 'The tuition fees for the Program',
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  tuitionFees: number;

  @ApiProperty({
    description: 'The application deadline for the Program',
    type: Date,
    required: true,
  })
  // ISO date string ("2024-12-01T00:00:00Z")
  @IsNotEmpty()
  @IsDateString()
  applicationDeadline: Date;

  @ApiProperty({
    description: 'The ID of the University offering the Program',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  universityId: string;
}
