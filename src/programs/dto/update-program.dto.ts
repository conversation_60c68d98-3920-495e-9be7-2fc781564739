import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateProgramDto {
  @ApiPropertyOptional({
    description: 'The name of the Program',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'The duration of the Program',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  duration: string;

  @ApiPropertyOptional({
    description: 'The type of degree awarded by the Program',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  degreeType: string;

  @ApiPropertyOptional({
    description: 'The tuition fees for the Program',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  tuitionFees: number;

  @ApiPropertyOptional({
    description: 'The application deadline for the Program',
    type: Date,
    required: false,
  })
  // ISO date string ("2024-12-01T00:00:00Z")
  @IsOptional()
  @IsDateString()
  applicationDeadline: Date;

  @ApiPropertyOptional({
    description: 'The ID of the University offering the Program',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  universityId: string;
}
