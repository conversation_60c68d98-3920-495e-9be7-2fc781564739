import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProgramDto } from './dto/create-program.dto';
import { ProgramDto } from './dto/program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';

@Injectable()
export class ProgramsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createProgramDto: CreateProgramDto): Promise<ProgramDto> {
    try {
      return this.prisma.program.create({
        data: {
          ...createProgramDto,
          applicationDeadline: new Date(createProgramDto.applicationDeadline),
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async programsByUniversity(id: string): Promise<ProgramDto[]> {
    try {
      return this.prisma.program.findMany({ where: { universityId: id } });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(): Promise<ProgramDto[]> {
    try {
      return this.prisma.program.findMany();
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<ProgramDto> {
    try {
      const program = await this.prisma.program.findUnique({
        where: { id },
      });

      if (!program) {
        throw new NotFoundException(`Program with id ${id} not found`);
      }

      return program;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: string,
    updateProgramDto: UpdateProgramDto,
  ): Promise<ProgramDto> {
    try {
      const program = await this.findOne(id);

      if (!program) {
        throw new NotFoundException(`Program with id ${id} not found`);
      }

      return this.prisma.program.update({
        where: { id },
        data: updateProgramDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async delete(id: string): Promise<ProgramDto> {
    try {
      await this.findOne(id);
      return this.prisma.program.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
