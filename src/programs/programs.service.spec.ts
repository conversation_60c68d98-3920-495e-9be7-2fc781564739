import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProgramDto } from './dto/create-program.dto';
import { ProgramDto } from './dto/program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { ProgramsService } from './programs.service';

describe('ProgramsService', () => {
  let service: ProgramsService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgramsService,
        {
          provide: PrismaService,
          useValue: {
            program: {
              create: jest.fn(),
              findMany: jest.fn(),
              findUnique: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ProgramsService>(ProgramsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new program', async () => {
      const createProgramDto: CreateProgramDto = {
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };
      const createdProgram: ProgramDto = {
        id: '1',
        ...createProgramDto,
      };

      prismaService.program.create = jest
        .fn()
        .mockResolvedValue(createdProgram);

      const result = await service.create(createProgramDto);
      expect(result).toEqual(createdProgram);
    });

    it('should throw an HttpException on error', async () => {
      const createProgramDto: CreateProgramDto = {
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };

      prismaService.program.create = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.create(createProgramDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('programsByUniversity', () => {
    it('should return programs by university', async () => {
      const programs: ProgramDto[] = [
        {
          id: '1',
          name: 'Test Program',
          duration: '2 years',
          degreeType: 'Bachelor',
          tuitionFees: 10000,
          applicationDeadline: new Date('2024-12-01T00:00:00Z'),
          universityId: '1',
        },
      ];

      prismaService.program.findMany = jest.fn().mockResolvedValue(programs);

      const result = await service.programsByUniversity('1');
      expect(result).toEqual(programs);
    });

    it('should throw an HttpException on error', async () => {
      prismaService.program.findMany = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.programsByUniversity('1')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('findAll', () => {
    it('should return all programs', async () => {
      const programs: ProgramDto[] = [
        {
          id: '1',
          name: 'Test Program',
          duration: '2 years',
          degreeType: 'Bachelor',
          tuitionFees: 10000,
          applicationDeadline: new Date('2024-12-01T00:00:00Z'),
          universityId: '1',
        },
      ];

      prismaService.program.findMany = jest.fn().mockResolvedValue(programs);

      const result = await service.findAll();
      expect(result).toEqual(programs);
    });

    it('should throw an HttpException on error', async () => {
      prismaService.program.findMany = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.findAll()).rejects.toThrow(HttpException);
    });
  });

  describe('findOne', () => {
    it('should return a program by id', async () => {
      const program: ProgramDto = {
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };

      prismaService.program.findUnique = jest.fn().mockResolvedValue(program);

      const result = await service.findOne('1');
      expect(result).toEqual(program);
    });

    it('should throw NotFoundException if program not found', async () => {
      prismaService.program.findUnique = jest.fn().mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });

    it('should throw an HttpException on error', async () => {
      prismaService.program.findUnique = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.findOne('1')).rejects.toThrow(HttpException);
    });
  });

  describe('update', () => {
    it('should update a program', async () => {
      const updateProgramDto: UpdateProgramDto = {
        name: 'Updated Program',
        duration: '3 years',
        degreeType: 'Master',
        tuitionFees: 15000,
        applicationDeadline: new Date('2025-12-01T00:00:00Z'),
        universityId: '1',
      };
      const updatedProgram: ProgramDto = {
        id: '1',
        name: updateProgramDto.name,
        duration: updateProgramDto.duration,
        degreeType: updateProgramDto.degreeType,
        tuitionFees: updateProgramDto.tuitionFees,
        applicationDeadline: updateProgramDto.applicationDeadline,
        universityId: updateProgramDto.universityId,
      };

      prismaService.program.findUnique = jest.fn().mockResolvedValue({
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      });
      prismaService.program.update = jest
        .fn()
        .mockResolvedValue(updatedProgram);

      const result = await service.update('1', updateProgramDto);
      expect(result).toEqual(updatedProgram);
    });

    it('should throw NotFoundException if program not found', async () => {
      const updateProgramDto: UpdateProgramDto = {
        name: 'Updated Program',
        duration: '3 years',
        degreeType: 'Master',
        tuitionFees: 15000,
        applicationDeadline: new Date('2025-12-01T00:00:00Z'),
        universityId: '1',
      };

      prismaService.program.findUnique = jest.fn().mockResolvedValue(null);

      await expect(service.update('1', updateProgramDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw an HttpException on error', async () => {
      const updateProgramDto: UpdateProgramDto = {
        name: 'Updated Program',
        duration: '3 years',
        degreeType: 'Master',
        tuitionFees: 15000,
        applicationDeadline: new Date('2025-12-01T00:00:00Z'),
        universityId: '1',
      };

      prismaService.program.findUnique = jest.fn().mockResolvedValue({
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      });
      prismaService.program.update = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.update('1', updateProgramDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('delete', () => {
    it('should delete a program', async () => {
      const program: ProgramDto = {
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };

      prismaService.program.findUnique = jest.fn().mockResolvedValue(program);
      prismaService.program.delete = jest.fn().mockResolvedValue(program);

      const result = await service.delete('1');
      expect(result).toEqual(program);
    });

    it('should throw NotFoundException if program not found', async () => {
      prismaService.program.findUnique = jest.fn().mockResolvedValue(null);

      await expect(service.delete('1')).rejects.toThrow(NotFoundException);
    });

    it('should throw an HttpException on error', async () => {
      prismaService.program.findUnique = jest.fn().mockResolvedValue({
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      });
      prismaService.program.delete = jest
        .fn()
        .mockRejectedValue(
          new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      await expect(service.delete('1')).rejects.toThrow(HttpException);
    });
  });
});
