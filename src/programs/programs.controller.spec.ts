import { Test, TestingModule } from '@nestjs/testing';
import { CreateProgramDto } from './dto/create-program.dto';
import { ProgramDto } from './dto/program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { ProgramsController } from './programs.controller';
import { ProgramsService } from './programs.service';

describe('ProgramsController', () => {
  let controller: ProgramsController;
  let service: ProgramsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProgramsController],
      providers: [
        {
          provide: ProgramsService,
          useValue: {
            create: jest.fn(),
            programsByUniversity: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ProgramsController>(ProgramsController);
    service = module.get<ProgramsService>(ProgramsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new program', async () => {
      const createProgramDto: CreateProgramDto = {
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };
      const createdProgram: ProgramDto = {
        id: '1',
        ...createProgramDto,
      };

      service.create = jest.fn().mockResolvedValue(createdProgram);

      const result = await controller.create(createProgramDto);
      expect(result).toEqual(createdProgram);
    });
  });

  describe('programsByUniversity', () => {
    it('should return programs by university', async () => {
      const programs: ProgramDto[] = [
        {
          id: '1',
          name: 'Test Program',
          duration: '2 years',
          degreeType: 'Bachelor',
          tuitionFees: 10000,
          applicationDeadline: new Date('2024-12-01T00:00:00Z'),
          universityId: '1',
        },
      ];

      service.programsByUniversity = jest.fn().mockResolvedValue(programs);

      const result = await controller.programsByUniversity('1');
      expect(result).toEqual(programs);
    });
  });

  describe('findAll', () => {
    it('should return all programs', async () => {
      const programs: ProgramDto[] = [
        {
          id: '1',
          name: 'Test Program',
          duration: '2 years',
          degreeType: 'Bachelor',
          tuitionFees: 10000,
          applicationDeadline: new Date('2024-12-01T00:00:00Z'),
          universityId: '1',
        },
      ];

      service.findAll = jest.fn().mockResolvedValue(programs);

      const result = await controller.findAll();
      expect(result).toEqual(programs);
    });
  });

  describe('findOne', () => {
    it('should return a program by id', async () => {
      const program: ProgramDto = {
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };

      service.findOne = jest.fn().mockResolvedValue(program);

      const result = await controller.findOne('1');
      expect(result).toEqual(program);
    });
  });

  describe('update', () => {
    it('should update a program', async () => {
      const updateProgramDto: UpdateProgramDto = {
        name: 'Updated Program',
        duration: '3 years',
        degreeType: 'Master',
        tuitionFees: 15000,
        applicationDeadline: new Date('2025-12-01T00:00:00Z'),
        universityId: '1',
      };
      const updatedProgram: ProgramDto = {
        id: '1',
        name: updateProgramDto.name,
        duration: updateProgramDto.duration,
        degreeType: updateProgramDto.degreeType,
        tuitionFees: updateProgramDto.tuitionFees,
        applicationDeadline: updateProgramDto.applicationDeadline,
        universityId: updateProgramDto.universityId,
      };

      service.update = jest.fn().mockResolvedValue(updatedProgram);

      const result = await controller.update('1', updateProgramDto);
      expect(result).toEqual(updatedProgram);
    });
  });

  describe('delete', () => {
    it('should delete a program', async () => {
      const program: ProgramDto = {
        id: '1',
        name: 'Test Program',
        duration: '2 years',
        degreeType: 'Bachelor',
        tuitionFees: 10000,
        applicationDeadline: new Date('2024-12-01T00:00:00Z'),
        universityId: '1',
      };

      service.delete = jest.fn().mockResolvedValue(program);

      const result = await controller.delete('1');
      expect(result).toEqual(program);
    });
  });
});
