import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { CreateProgramDto } from './dto/create-program.dto';
import { ProgramDto } from './dto/program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { ProgramsService } from './programs.service';

@ApiBearerAuth()
@ApiTags('Programs')
@Controller('programs')
export class ProgramsController {
  constructor(private readonly programService: ProgramsService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new Program created successfully',
    type: ProgramDto,
  })
  @Permissions('create:universities')
  create(@Body() createProgramDto: CreateProgramDto): Promise<ProgramDto> {
    try {
      return this.programService.create(createProgramDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all Programs.',
    type: [ProgramDto],
  })
  @Permissions('read:universities')
  findAll(): Promise<ProgramDto[]> {
    try {
      return this.programService.findAll();
    } catch (error) {
      throw error;
    }
  }

  @Get('/university/:id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved Programs of a University.',
    type: ProgramDto,
  })
  @Permissions('read:universities')
  programsByUniversity(@Param('id') id: string): Promise<ProgramDto[]> {
    try {
      return this.programService.programsByUniversity(id);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved a Program.',
    type: ProgramDto,
  })
  @Permissions('read:universities')
  findOne(@Param('id') id: string): Promise<ProgramDto> {
    try {
      return this.programService.findOne(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'Program updated successfully.',
    type: ProgramDto,
  })
  @Permissions('update:universities')
  update(
    @Param('id') id: string,
    @Body() updateProgramDto: UpdateProgramDto,
  ): Promise<ProgramDto> {
    try {
      return this.programService.update(id, updateProgramDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'Program deleted successfully.',
    type: ProgramDto,
  })
  @Permissions('delete:universities')
  delete(@Param('id') id: string): Promise<ProgramDto> {
    try {
      return this.programService.delete(id);
    } catch (error) {
      throw error;
    }
  }
}
