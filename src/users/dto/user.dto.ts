import { ApiProperty } from '@nestjs/swagger';
import { $Enums, User, UserStatus } from '@prisma/client';

export class UserDto implements Omit<User, 'password'> {
  @ApiProperty({ description: 'User ID', type: String })
  name: string;

  @ApiProperty({
    description: 'The id of the user',
    type: String,
  })
  id: string;

  @ApiProperty({ description: 'User Email', type: String })
  email: string;

  // @ApiProperty({ description: 'User AutoTaskId', type: String })
  // uid: string;

  @ApiProperty({
    description: 'User Status',
    enum: $Enums.UserStatus,
  })
  status: UserStatus;

  @ApiProperty({
    description: 'The created at date of the user',
    type: String,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last updated at date of the user',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'The roles of the user',
  })
  roles: string[];

  @ApiProperty({
    description: 'The permissions of the user, mapped from roles',
  })
  permissions: string[]; // permissions will be derived from roles
}
