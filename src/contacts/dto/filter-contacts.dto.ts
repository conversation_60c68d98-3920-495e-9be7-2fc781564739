import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsIn, IsNumber, IsOptional, IsString, Min } from 'class-validator';

export class FilterContactsDto {
  @ApiProperty({
    description: 'Search query for contacts',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Sort by given fields',
    required: false,
    type: String,
    default: 'createdAt',
  })
  @IsOptional()
  @IsIn(['name', 'createdAt', 'updatedAt'])
  @Transform(({ value }) => value ?? 'createdAt')
  sortBy?: 'name' | 'createdAt' | 'updatedAt' = 'createdAt';

  @ApiProperty({
    description: 'Order by given fields',
    required: false,
    type: String,
    default: 'asc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  @Transform(({ value }) => value ?? 'asc')
  order?: 'asc' | 'desc' = 'asc';

  @ApiProperty({
    description: 'Page number of the search results',
    required: false,
    type: Number,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => (value ? Number(value) : 1))
  page?: number = 1;

  @ApiProperty({
    description: 'Number of results per page',
    required: false,
    type: Number,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => (value ? Number(value) : 10))
  limit?: number = 10;
}
