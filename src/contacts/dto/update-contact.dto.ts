import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateContactDto {
  @ApiProperty({
    description: 'Contact name',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Contact phone no',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Contact designation at the University',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  designation?: string;

  @ApiProperty({
    description: 'Contact department at the University',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  department?: string;
}
