import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class ContactDto {
  @ApiProperty({
    description: 'Contact id',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Contact name',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Contact email',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Contact phone no',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Contact designation at the University',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  designation?: string;

  @ApiProperty({
    description: 'Contact department at the University',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiProperty({
    description: 'University id of the Contact',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsUUID()
  universityId: string;
}
