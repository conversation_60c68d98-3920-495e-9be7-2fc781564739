import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { ContactsService } from './contacts.service';
import { ContactDto } from './dto/contact.dto';
import { CreateContactDto } from './dto/create-contact.dto';
import { FilterContactsDto } from './dto/filter-contacts.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

@ApiBearerAuth()
@ApiTags('Contacts')
@Controller('contacts')
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: 'A new Contact created successfully.',
    type: ContactDto,
  })
  @Permissions('create:contacts')
  async createContact(
    @Body() createContactDto: CreateContactDto,
  ): Promise<ContactDto> {
    try {
      return this.contactsService.createContact(createContactDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiResponse({
    status: 200,
    description: 'All Contacts retrieved successfully.',
    type: ContactDto,
  })
  @Permissions('READ')
  async getAllContacts(
    @Query() filterDto: FilterContactsDto,
  ): Promise<ContactDto[]> {
    try {
      return this.contactsService.getContacts(
        filterDto.search,
        filterDto.sortBy,
        filterDto.order,
        filterDto.page,
        filterDto.limit,
      );
    } catch (error) {
      throw error;
    }
  }

  @Get('university/:id')
  @ApiResponse({
    status: 200,
    description: 'All Contacts retrieved successfully.',
    type: ContactDto,
  })
  @Permissions('READ')
  async getContactsByUniversityId(
    @Query() filterDto: FilterContactsDto,
    @Param('id') id: string,
  ): Promise<ContactDto[]> {
    try {
      return this.contactsService.getContactsByUniversityId(
        id,
        filterDto.search,
        filterDto.sortBy,
        filterDto.order,
        filterDto.page,
        filterDto.limit,
      );
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: 'A Contact retrieved successfully.',
    type: ContactDto,
  })
  @Permissions('READ')
  async getContactById(@Param('id') id: string): Promise<ContactDto> {
    try {
      return this.contactsService.getContactById(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @ApiResponse({
    status: 200,
    description: 'A Contact has been updated successfully.',
    type: ContactDto,
  })
  @Permissions('UPDATE')
  async updateContact(
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
  ): Promise<ContactDto> {
    try {
      return this.contactsService.updateContact(id, updateContactDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'A Contact has been deleted successfully.',
    type: ContactDto,
  })
  @Permissions('DELETE')
  async deleteContact(@Param('id') id: string): Promise<ContactDto> {
    try {
      return this.contactsService.deleteContact(id);
    } catch (error) {
      throw error;
    }
  }
}
