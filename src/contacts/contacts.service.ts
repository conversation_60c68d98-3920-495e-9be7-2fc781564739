import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from 'src/prisma/prisma.service';
import { ContactDto } from './dto/contact.dto';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

@Injectable()
export class ContactsService {
  constructor(private prisma: PrismaService) {}

  async createContact(data: CreateContactDto): Promise<ContactDto> {
    try {
      return await this.prisma.contact.create({
        data,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Email already exists');
        }
      } else if (error instanceof ConflictException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getContacts(
    searchQuery?: string,
    sortBy: 'name' | 'createdAt' | 'updatedAt' = 'createdAt',
    order: 'asc' | 'desc' = 'desc',
    page: number = 1,
    limit: number = 10,
  ): Promise<ContactDto[]> {
    try {
      const skip = (page - 1) * limit;

      return await this.prisma.contact.findMany({
        where: searchQuery
          ? {
              OR: [
                { name: { contains: searchQuery, mode: 'insensitive' } },
                { email: { contains: searchQuery, mode: 'insensitive' } },
                { phone: { contains: searchQuery, mode: 'insensitive' } },
                {
                  university: {
                    name: { contains: searchQuery, mode: 'insensitive' },
                  },
                },
              ],
            }
          : undefined,
        orderBy: { [sortBy]: order },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getContactsByUniversityId(
    id: string,
    searchQuery?: string,
    sortBy: 'name' | 'createdAt' | 'updatedAt' = 'createdAt',
    order: 'asc' | 'desc' = 'desc',
    page: number = 1,
    limit: number = 10,
  ): Promise<ContactDto[]> {
    try {
      const skip = (page - 1) * limit;

      return await this.prisma.contact.findMany({
        where: {
          universityId: id,
          ...(searchQuery
            ? {
                OR: [
                  { name: { contains: searchQuery, mode: 'insensitive' } },
                  { email: { contains: searchQuery, mode: 'insensitive' } },
                  { phone: { contains: searchQuery, mode: 'insensitive' } },
                  {
                    university: {
                      name: { contains: searchQuery, mode: 'insensitive' },
                    },
                  },
                ],
              }
            : undefined),
        },
        orderBy: { [sortBy]: order },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to fetch contacts: ${error.message}`,
      );
    }
  }

  async getContactById(id: string): Promise<ContactDto> {
    try {
      const contact = await this.prisma.contact.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      if (!contact) throw new NotFoundException('Contact not found');
      return contact;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException('Record not found');
        }
      } else if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateContact(id: string, data: UpdateContactDto): Promise<ContactDto> {
    try {
      return await this.prisma.contact.update({
        where: { id },
        data,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException('Record not found');
        }
      } else if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteContact(id: string): Promise<ContactDto> {
    try {
      const contact = await this.prisma.contact.delete({
        where: { id },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          department: true,
          designation: true,
          universityId: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!contact) {
        throw new HttpException(
          `Contact with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return contact;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException('Record not found');
        }
      } else if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
