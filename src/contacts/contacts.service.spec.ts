import { ConflictException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from 'src/prisma/prisma.service';
import { ContactsService } from './contacts.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

describe('ContactsService', () => {
  let service: ContactsService;
  let prisma: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContactsService,
        {
          provide: PrismaService,
          useValue: {
            contact: {
              create: jest.fn(),
              findMany: jest.fn(),
              findUnique: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ContactsService>(ContactsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createContact', () => {
    it('should create a contact', async () => {
      const createContactDto: CreateContactDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
      };
      const createdContact = {
        id: 'some-uuid',
        ...createContactDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      prisma.contact.create = jest.fn().mockResolvedValue(createdContact);

      const result = await service.createContact(createContactDto);
      expect(result).toEqual(createdContact);
    });

    it('should throw ConflictException if email already exists', async () => {
      const createContactDto: CreateContactDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
      };
      prisma.contact.create = jest.fn().mockRejectedValue(
        new PrismaClientKnownRequestError('Email already exists', {
          code: 'P2002',
          clientVersion: '1.0.0',
        }),
      );

      await expect(service.createContact(createContactDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('getContacts', () => {
    it('should return a list of contacts', async () => {
      const contacts = [
        {
          id: 'some-uuid',
          name: 'John Doe',
          email: '<EMAIL>',
          universityId: 'some-uuid',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      prisma.contact.findMany = jest.fn().mockResolvedValue(contacts);

      const result = await service.getContacts();
      expect(result).toEqual(contacts);
    });
  });

  describe('getContactsByUniversityId', () => {
    it('should return a list of contacts by university id', async () => {
      const contacts = [
        {
          id: 'some-uuid',
          name: 'John Doe',
          email: '<EMAIL>',
          universityId: 'some-uuid',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      prisma.contact.findMany = jest.fn().mockResolvedValue(contacts);

      const result = await service.getContactsByUniversityId('some-uuid');
      expect(result).toEqual(contacts);
    });
  });

  describe('getContactById', () => {
    it('should return a contact by id', async () => {
      const contact = {
        id: 'some-uuid',
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      prisma.contact.findUnique = jest.fn().mockResolvedValue(contact);

      const result = await service.getContactById('some-uuid');
      expect(result).toEqual(contact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      prisma.contact.findUnique = jest.fn().mockResolvedValue(null);

      await expect(service.getContactById('some-uuid')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateContact', () => {
    it('should update a contact', async () => {
      const updateContactDto: UpdateContactDto = { name: 'Jane Doe' };
      const updatedContact = {
        id: 'some-uuid',
        name: 'Jane Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      prisma.contact.update = jest.fn().mockResolvedValue(updatedContact);

      const result = await service.updateContact('some-uuid', updateContactDto);
      expect(result).toEqual(updatedContact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      const updateContactDto: UpdateContactDto = { name: 'Jane Doe' };
      prisma.contact.update = jest.fn().mockRejectedValue(
        new PrismaClientKnownRequestError('Record not found', {
          code: 'P2025',
          clientVersion: '1.0.0',
        }),
      );

      await expect(
        service.updateContact('some-uuid', updateContactDto),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteContact', () => {
    it('should delete a contact', async () => {
      const contact = {
        id: 'some-uuid',
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      prisma.contact.delete = jest.fn().mockResolvedValue(contact);

      const result = await service.deleteContact('some-uuid');
      expect(result).toEqual(contact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      prisma.contact.delete = jest.fn().mockRejectedValue(
        new PrismaClientKnownRequestError('Record not found', {
          code: 'P2025',
          clientVersion: '1.0.0',
        }),
      );

      await expect(service.deleteContact('some-uuid')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
