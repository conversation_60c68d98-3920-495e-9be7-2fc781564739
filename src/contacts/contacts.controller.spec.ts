import { Test, TestingModule } from '@nestjs/testing';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { FilterContactsDto } from './dto/filter-contacts.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

describe('ContactsController', () => {
  let controller: ContactsController;
  let service: ContactsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContactsController],
      providers: [
        {
          provide: ContactsService,
          useValue: {
            createContact: jest.fn(),
            getContacts: jest.fn(),
            getContactsByUniversityId: jest.fn(),
            getContactById: jest.fn(),
            updateContact: jest.fn(),
            deleteContact: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ContactsController>(ContactsController);
    service = module.get<ContactsService>(ContactsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createContact', () => {
    it('should create a contact', async () => {
      const createContactDto: CreateContactDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
      };
      const createdContact = {
        id: 'some-uuid',
        ...createContactDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      service.createContact = jest.fn().mockResolvedValue(createdContact);

      const result = await controller.createContact(createContactDto);
      expect(result).toEqual(createdContact);
    });
  });

  describe('getAllContacts', () => {
    it('should return a list of contacts', async () => {
      const filterDto: FilterContactsDto = {
        search: 'John',
        sortBy: 'name',
        order: 'asc',
        page: 1,
        limit: 10,
      };
      const contacts = [
        {
          id: 'some-uuid',
          name: 'John Doe',
          email: '<EMAIL>',
          universityId: 'some-uuid',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      service.getContacts = jest.fn().mockResolvedValue(contacts);

      const result = await controller.getAllContacts(filterDto);
      expect(result).toEqual(contacts);
    });
  });

  describe('getContactsByUniversityId', () => {
    it('should return a list of contacts by university id', async () => {
      const filterDto: FilterContactsDto = {
        search: 'John',
        sortBy: 'name',
        order: 'asc',
        page: 1,
        limit: 10,
      };
      const contacts = [
        {
          id: 'some-uuid',
          name: 'John Doe',
          email: '<EMAIL>',
          universityId: 'some-uuid',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      service.getContactsByUniversityId = jest.fn().mockResolvedValue(contacts);

      const result = await controller.getContactsByUniversityId(
        filterDto,
        'some-uuid',
      );
      expect(result).toEqual(contacts);
    });
  });

  describe('getContactById', () => {
    it('should return a contact by id', async () => {
      const contact = {
        id: 'some-uuid',
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      service.getContactById = jest.fn().mockResolvedValue(contact);

      const result = await controller.getContactById('some-uuid');
      expect(result).toEqual(contact);
    });
  });

  describe('updateContact', () => {
    it('should update a contact', async () => {
      const updateContactDto: UpdateContactDto = { name: 'Jane Doe' };
      const updatedContact = {
        id: 'some-uuid',
        name: 'Jane Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      service.updateContact = jest.fn().mockResolvedValue(updatedContact);

      const result = await controller.updateContact(
        'some-uuid',
        updateContactDto,
      );
      expect(result).toEqual(updatedContact);
    });
  });

  describe('deleteContact', () => {
    it('should delete a contact', async () => {
      const contact = {
        id: 'some-uuid',
        name: 'John Doe',
        email: '<EMAIL>',
        universityId: 'some-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      service.deleteContact = jest.fn().mockResolvedValue(contact);

      const result = await controller.deleteContact('some-uuid');
      expect(result).toEqual(contact);
    });
  });
});
