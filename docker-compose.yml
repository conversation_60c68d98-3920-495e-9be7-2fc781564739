services:
  db:
    image: postgres:alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - DB_VOL:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    expose:
      - 5432

  backend:
    build: .
    container_name: fes_crm
    restart: unless-stopped
    depends_on:
      - db
    environment:
      NODE_ENV: production
      DATABASE_URL: postgres://${DB_USER}:${DB_PASSWORD}@db:5432/${DB_NAME}
      BACKEND_PORT: 5000
    ports:
      - '5000:5000'
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    command: ['/bin/sh', '-c', '/usr/src/app/entrypoint.sh']

volumes:
  DB_VOL:
